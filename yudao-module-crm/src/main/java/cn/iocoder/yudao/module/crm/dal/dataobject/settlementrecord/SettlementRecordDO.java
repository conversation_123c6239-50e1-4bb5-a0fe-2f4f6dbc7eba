package cn.iocoder.yudao.module.crm.dal.dataobject.settlementrecord;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * CRM结算单 DO
 *
 * <AUTHOR>
 */
@TableName("crm_settlement_record")
@KeySequence("crm_settlement_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 结算日期
     */
    private LocalDate settlementDate;
    /**
     * 品名款号
     */
    private String productNameCode;
    /**
     * 成分
     */
    private String composition;
    /**
     * 规格
     */
    private String specification;
    /**
     * 克重
     */
    private BigDecimal weight;
    /**
     * 幅宽
     */
    private String width;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 单位
     *
     * 枚举 {@link TODO crm_product_unit 对应的类}
     */
    private Integer unit;
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    /**
     * 金额
     */
    private BigDecimal totalAmount;
    /**
     * 备注
     */
    private String remark;

    /**
     * 合同编号
     *
     * 关联 {@link cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractDO#getId()}
     */
    private Long contractId;

    /**
     * 客户编号
     *
     * 关联 {@link cn.iocoder.yudao.module.crm.dal.dataobject.customer.CrmCustomerDO#getId()}
     */
    private Long customerId;

    /**
     * 负责人的用户编号
     *
     * 关联 AdminUserDO 的 id 字段
     */
    private Long ownerUserId;


}