package cn.iocoder.yudao.module.crm.service.settlementrecord;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractDO;
import cn.iocoder.yudao.module.crm.service.contract.CrmContractService;
import cn.iocoder.yudao.module.crm.service.customer.CrmCustomerService;
import cn.iocoder.yudao.module.crm.service.permission.CrmPermissionService;
import cn.iocoder.yudao.module.crm.service.permission.bo.CrmPermissionCreateReqBO;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.enums.permission.CrmPermissionLevelEnum;
import cn.iocoder.yudao.module.crm.framework.permission.core.annotations.CrmPermission;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Date;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo.*;
import cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo.SettlementRecordRespVO;
import cn.iocoder.yudao.module.crm.dal.dataobject.settlementrecord.SettlementRecordDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;

import cn.iocoder.yudao.module.crm.dal.mysql.settlementrecord.SettlementRecordMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.crm.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * CRM结算单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SettlementRecordServiceImpl implements SettlementRecordService {

    @Resource
    private SettlementRecordMapper settlementRecordMapper;
    @Resource
    private CrmContractService contractService;
    @Resource
    private CrmCustomerService customerService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private CrmPermissionService permissionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSettlementRecord(SettlementRecordSaveReqVO createReqVO) {
        // 1. 校验关联数据存在
        validateRelationDataExists(createReqVO);

        // 2. 插入结算单
        SettlementRecordDO settlementRecord = BeanUtils.toBean(createReqVO, SettlementRecordDO.class);
        settlementRecordMapper.insert(settlementRecord);

        // 3. 创建数据权限
        permissionService.createPermission(new CrmPermissionCreateReqBO()
                .setBizType(CrmBizTypeEnum.CRM_SETTLEMENT_RECORD.getType())
                .setBizId(settlementRecord.getId())
                .setUserId(createReqVO.getOwnerUserId())
                .setLevel(CrmPermissionLevelEnum.OWNER.getLevel())); // 设置当前操作的人为负责人

        // 4. 返回
        return settlementRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_SETTLEMENT_RECORD, bizId = "#updateReqVO.id", level = CrmPermissionLevelEnum.WRITE)
    public void updateSettlementRecord(SettlementRecordSaveReqVO updateReqVO) {
        // 1. 校验存在
        SettlementRecordDO settlementRecord = validateSettlementRecordExists(updateReqVO.getId());
        // 不允许修改关联字段
        updateReqVO.setOwnerUserId(settlementRecord.getOwnerUserId())
                .setCustomerId(settlementRecord.getCustomerId())
                .setContractId(settlementRecord.getContractId());

        // 2. 更新
        SettlementRecordDO updateObj = BeanUtils.toBean(updateReqVO, SettlementRecordDO.class);
        settlementRecordMapper.updateById(updateObj);
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_SETTLEMENT_RECORD, bizId = "#id", level = CrmPermissionLevelEnum.OWNER)
    public void deleteSettlementRecord(Long id) {
        // 校验存在
        validateSettlementRecordExists(id);
        // 删除
        settlementRecordMapper.deleteById(id);
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_SETTLEMENT_RECORD, bizId = "#ids", level = CrmPermissionLevelEnum.OWNER)
    public void deleteSettlementRecordListByIds(List<Long> ids) {
        // 校验存在
        validateSettlementRecordExists(ids);
        // 删除
        settlementRecordMapper.deleteByIds(ids);
    }

    private void validateSettlementRecordExists(List<Long> ids) {
        List<SettlementRecordDO> list = settlementRecordMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(SETTLEMENT_RECORD_NOT_EXISTS);
        }
    }

    private SettlementRecordDO validateSettlementRecordExists(Long id) {
        SettlementRecordDO settlementRecord = settlementRecordMapper.selectById(id);
        if (settlementRecord == null) {
            throw exception(SETTLEMENT_RECORD_NOT_EXISTS);
        }
        return settlementRecord;
    }

    /**
     * 校验关联数据是否存在
     *
     * @param reqVO 请求
     */
    private void validateRelationDataExists(SettlementRecordSaveReqVO reqVO) {
        // 1. 校验负责人
        if (reqVO.getOwnerUserId() != null) {
            adminUserApi.validateUser(reqVO.getOwnerUserId());
        }
        // 2. 校验合同
        if (reqVO.getContractId() != null) {
            CrmContractDO contract = contractService.validateContract(reqVO.getContractId());
            reqVO.setCustomerId(contract.getCustomerId()); // 设置客户编号
        }
        // 3. 校验客户（通过合同自动设置，这里只是验证）
        if (reqVO.getCustomerId() != null) {
            customerService.validateCustomer(reqVO.getCustomerId());
        }
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_SETTLEMENT_RECORD, bizId = "#id", level = CrmPermissionLevelEnum.READ)
    public SettlementRecordDO getSettlementRecord(Long id) {
        return settlementRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SettlementRecordDO> getSettlementRecordPage(SettlementRecordPageReqVO pageReqVO) {
        return settlementRecordMapper.selectPage(pageReqVO, getLoginUserId(), pageReqVO.getSceneType());
    }

    @Override
    public void exportSettlementRecordExcel(List<SettlementRecordDO> list, HttpServletResponse response) throws IOException {
        log.info("开始导出结算单，数据条数：{}", list.size());

        // 1. 读取模板文件
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("excel/settlement_template.xlsx");
        if (templateStream == null) {
            log.warn("结算单模板文件不存在，使用默认导出");
            // 如果模板文件不存在，使用默认导出
            ExcelUtils.write(response, "CRM结算单.xls", "数据", SettlementRecordRespVO.class,
                    BeanUtils.toBean(list, SettlementRecordRespVO.class));
            return;
        }

        log.info("模板文件读取成功，开始处理Excel");

        try (Workbook workbook = new XSSFWorkbook(templateStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            log.info("Excel工作表创建成功，工作表名：{}", sheet.getSheetName());

            // 2. 获取合同和客户信息（用于填入第7行）
            String contractNo = "";
            String customerName = "";
            if (!list.isEmpty() && list.get(0).getContractId() != null) {
                try {
                    CrmContractDO contractInfo = contractService.getContract(list.get(0).getContractId());
                    contractNo = contractInfo.getNo();
                    if (contractInfo.getCustomerId() != null) {
                        customerName = customerService.getCustomer(contractInfo.getCustomerId()).getName();
                    }
                } catch (Exception e) {
                    log.warn("获取合同和客户信息失败", e);
                }
            }

            // 3. 修改第7行的信息
            Row row7 = sheet.getRow(6); // 第7行（索引从0开始）
            if (row7 != null) {
                // 创建统一的样式：字号11，垂直居中
                CellStyle unifiedStyle = workbook.createCellStyle();
                Font unifiedFont = workbook.createFont();
                unifiedFont.setFontHeightInPoints((short) 11); // 字号11
                unifiedStyle.setFont(unifiedFont);
                unifiedStyle.setAlignment(HorizontalAlignment.LEFT); // 水平居左
                unifiedStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                // 在第2-4列合并显示收货单位
                Cell customerCell = createTextCell(row7, 1, "收货单位：" + customerName);
                customerCell.setCellStyle(unifiedStyle);
                // 合并第2-4列
                sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(6, 6, 1, 3));

                // 在第6列显示合同编号
                Cell contractCell = createTextCell(row7, 5, "合同编号：" + contractNo);
                contractCell.setCellStyle(unifiedStyle);
            }

            // 4. 处理第8行的表头（删除合同号列并保留背景色）
            Row row8 = sheet.getRow(7); // 第8行（表头行）
            CellStyle headerStyle = null;
            if (row8 != null) {
                // 获取原有的表头样式（从第2列获取）
                Cell sampleCell = row8.getCell(1);
                if (sampleCell != null) {
                    headerStyle = sampleCell.getCellStyle();
                }

                // 重新设置表头内容，删除合同号列
                String[] newHeaders = {"日期", "品名/货号", "成分", "规格", "克重", "幅宽", "数量", "单位", "单价", "金额", "备注"};

                // 清除原有的表头单元格
                for (int i = 1; i < row8.getLastCellNum(); i++) {
                    Cell cell = row8.getCell(i);
                    if (cell != null) {
                        row8.removeCell(cell);
                    }
                }

                // 重新创建表头单元格
                for (int i = 0; i < newHeaders.length; i++) {
                    Cell newCell = createTextCell(row8, i + 1, newHeaders[i]); // 从第2列开始（索引1）
                    if (headerStyle != null) {
                        newCell.setCellStyle(headerStyle);
                    }
                }
            }

            // 4. 删除模板中的数据行（第8-18行）
            for (int i = 17; i >= 8; i--) { // 从后往前删除，避免索引变化
                Row row = sheet.getRow(i);
                if (row != null) {
                    sheet.removeRow(row);
                }
            }

            // 5. 填入数据（删除合同号列后，从第2列开始）
            int rowIndex = 8; // 从第9行开始填入数据（索引从0开始）
            BigDecimal totalAmount = BigDecimal.ZERO;

            for (SettlementRecordDO record : list) {
                Row dataRow = sheet.createRow(rowIndex++);

                // 设置产品行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
                dataRow.setHeight((short) (25 * 20));

                // 填入各列数据（删除合同号列后，列位置向前移动）
                // 第2列：日期（格式：2025/8/18）
                if (record.getSettlementDate() != null) {
                    String dateStr = record.getSettlementDate().toString().replace("-", "/");
                    createTextCell(dataRow, 1, dateStr);
                } else {
                    createTextCell(dataRow, 1, "");
                }

                // 第3列：品名/货号
                createTextCell(dataRow, 2, record.getProductNameCode() != null ? record.getProductNameCode() : "");

                // 第4列：成分
                createTextCell(dataRow, 3, record.getComposition() != null ? record.getComposition() : "");

                // 第5列：规格
                createTextCell(dataRow, 4, record.getSpecification() != null ? record.getSpecification() : "");

                // 第6列：克重
                createTextCell(dataRow, 5, record.getWeight() != null ? record.getWeight().toString() : "");

                // 第7列：幅宽
                createTextCell(dataRow, 6, record.getWidth() != null ? record.getWidth() : "");

                // 第8列：数量（数字类型）
                if (record.getQuantity() != null) {
                    createNumberCell(dataRow, 7, record.getQuantity());
                } else {
                    createNumberCell(dataRow, 7, BigDecimal.ZERO);
                }

                // 第9列：单位
                createTextCell(dataRow, 8, "米");

                // 第10列：单价（数字类型）
                if (record.getUnitPrice() != null) {
                    createNumberCell(dataRow, 9, record.getUnitPrice());
                } else {
                    createNumberCell(dataRow, 9, BigDecimal.ZERO);
                }

                // 第11列：金额（数字类型）
                if (record.getTotalAmount() != null) {
                    createNumberCell(dataRow, 10, record.getTotalAmount());
                    totalAmount = totalAmount.add(record.getTotalAmount());
                } else {
                    createNumberCell(dataRow, 10, BigDecimal.ZERO);
                }

                // 第12列：备注
                createTextCell(dataRow, 11, record.getRemark() != null ? record.getRemark() : "");
            }

            // 6. 添加合计行
            Row totalRow = sheet.createRow(rowIndex);

            // 设置合计行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
            totalRow.setHeight((short) (25 * 20));

            createTextCell(totalRow, 9, "合计："); // 在J列（索引9）显示“合计：”
            createNumberCell(totalRow, 10, totalAmount); // 在金额列显示合计（数字类型）

            // 7. 为表格数据区域添加边框（第8行到最后一行，B列到L列）
            addTableBorders(sheet, workbook, 7, rowIndex, 1, 11); // 行索引从0开始，列索引从0开始

            // 5. 输出文件
            String fileName = "结算单_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            // 支持中文文件名
            try {
                String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            } catch (UnsupportedEncodingException e) {
                response.setHeader("Content-Disposition", "attachment; filename=settlement.xlsx");
            }

            log.info("导出结算单文件：{}", fileName);

            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
            log.info("结算单导出完成，文件大小：{} bytes", response.getOutputStream());
            log.info("结算单导出成功！");
        } finally {
            IoUtil.close(templateStream);
        }
    }

    /**
     * 创建文本单元格
     */
    private Cell createTextCell(Row row, int column, String value) {
        Cell cell = row.createCell(column, CellType.STRING);
        cell.setCellValue(value != null ? value : "");
        return cell;
    }

    /**
     * 创建数字单元格
     */
    private void createNumberCell(Row row, int column, BigDecimal value) {
        Cell cell = row.createCell(column, CellType.NUMERIC);
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0.0);
        }
    }

    /**
     * 创建单元格（兼容旧方法）
     */
    private void createCell(Row row, int column, String value) {
        createTextCell(row, column, value);
    }

    /**
     * 为表格区域添加边框
     */
    private void addTableBorders(Sheet sheet, Workbook workbook, int startRow, int endRow, int startCol, int endCol) {
        // 创建边框样式
        CellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);

        // 为指定区域的所有单元格添加边框
        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            for (int colIndex = startCol; colIndex <= endCol; colIndex++) {
                Cell cell = row.getCell(colIndex);
                if (cell == null) {
                    cell = row.createCell(colIndex);
                }

                // 获取原有样式并复制
                CellStyle originalStyle = cell.getCellStyle();
                CellStyle newStyle = workbook.createCellStyle();

                // 复制原有样式属性
                if (originalStyle != null) {
                    newStyle.cloneStyleFrom(originalStyle);
                }

                // 添加边框
                newStyle.setBorderTop(BorderStyle.THIN);
                newStyle.setBorderBottom(BorderStyle.THIN);
                newStyle.setBorderLeft(BorderStyle.THIN);
                newStyle.setBorderRight(BorderStyle.THIN);

                cell.setCellStyle(newStyle);
            }
        }
    }

}
