package cn.iocoder.yudao.module.crm.service.yardlist;

import java.util.*;
import javax.validation.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo.*;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * CRM码单主 Service 接口
 *
 * <AUTHOR>
 */
public interface YardListService {

    /**
     * 创建CRM码单主
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createYardList(@Valid YardListSaveReqVO createReqVO);

    /**
     * 更新CRM码单主
     *
     * @param updateReqVO 更新信息
     */
    void updateYardList(@Valid YardListSaveReqVO updateReqVO);

    /**
     * 删除CRM码单主
     *
     * @param id 编号
     */
    void deleteYardList(Long id);

    /**
    * 批量删除CRM码单主
    *
    * @param ids 编号
    */
    void deleteYardListListByIds(List<Long> ids);

    /**
     * 获得CRM码单主
     *
     * @param id 编号
     * @return CRM码单主
     */
    YardListDO getYardList(Long id);

    /**
     * 获得CRM码单主分页
     *
     * @param pageReqVO 分页查询
     * @return CRM码单主分页
     */
    PageResult<YardListDO> getYardListPage(YardListPageReqVO pageReqVO);

    // ==================== 子表（CRM码单子表（款号包号详情）） ====================

    /**
     * 获得CRM码单子表（款号包号详情）列表
     *
     * @param yardListId 码单主表ID
     * @return CRM码单子表（款号包号详情）列表
     */
    List<YardListDetailDO> getYardListDetailListByYardListId(Long yardListId);

    /**
     * 导出码单Excel
     *
     * @param list 码单列表
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportYardListExcel(List<YardListDO> list, HttpServletResponse response) throws IOException;

}