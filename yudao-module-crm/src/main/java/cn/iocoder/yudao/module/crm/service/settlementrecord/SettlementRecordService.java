package cn.iocoder.yudao.module.crm.service.settlementrecord;

import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo.*;
import cn.iocoder.yudao.module.crm.dal.dataobject.settlementrecord.SettlementRecordDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * CRM结算单 Service 接口
 *
 * <AUTHOR>
 */
public interface SettlementRecordService {

    /**
     * 创建CRM结算单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSettlementRecord(@Valid SettlementRecordSaveReqVO createReqVO);

    /**
     * 更新CRM结算单
     *
     * @param updateReqVO 更新信息
     */
    void updateSettlementRecord(@Valid SettlementRecordSaveReqVO updateReqVO);

    /**
     * 删除CRM结算单
     *
     * @param id 编号
     */
    void deleteSettlementRecord(Long id);

    /**
    * 批量删除CRM结算单
    *
    * @param ids 编号
    */
    void deleteSettlementRecordListByIds(List<Long> ids);

    /**
     * 获得CRM结算单
     *
     * @param id 编号
     * @return CRM结算单
     */
    SettlementRecordDO getSettlementRecord(Long id);

    /**
     * 获得CRM结算单分页
     *
     * @param pageReqVO 分页查询
     * @return CRM结算单分页
     */
    PageResult<SettlementRecordDO> getSettlementRecordPage(SettlementRecordPageReqVO pageReqVO);

    /**
     * 导出CRM结算单Excel
     *
     * @param list 结算单列表
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportSettlementRecordExcel(List<SettlementRecordDO> list, HttpServletResponse response) throws IOException;

}