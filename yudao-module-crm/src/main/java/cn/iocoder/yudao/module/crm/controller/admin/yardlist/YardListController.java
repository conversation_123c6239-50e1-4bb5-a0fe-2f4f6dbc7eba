package cn.iocoder.yudao.module.crm.controller.admin.yardlist;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo.*;
import cn.iocoder.yudao.module.crm.controller.admin.permission.vo.CrmPermissionRespVO;
import cn.iocoder.yudao.module.crm.dal.dataobject.permission.CrmPermissionDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDetailDO;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.service.permission.CrmPermissionService;
import cn.iocoder.yudao.module.crm.service.yardlist.YardListService;

@Tag(name = "管理后台 - CRM码单主")
@RestController
@RequestMapping("/crm/yard-list")
@Validated
public class YardListController {

    @Resource
    private YardListService yardListService;
    @Resource
    private CrmPermissionService permissionService;

    @PostMapping("/create")
    @Operation(summary = "创建CRM码单主")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:create')")
    public CommonResult<Long> createYardList(@Valid @RequestBody YardListSaveReqVO createReqVO) {
        return success(yardListService.createYardList(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新CRM码单主")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:update')")
    public CommonResult<Boolean> updateYardList(@Valid @RequestBody YardListSaveReqVO updateReqVO) {
        yardListService.updateYardList(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除CRM码单主")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('crm:yard-list:delete')")
    public CommonResult<Boolean> deleteYardList(@RequestParam("id") Long id) {
        yardListService.deleteYardList(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除CRM码单主")
                @PreAuthorize("@ss.hasPermission('crm:yard-list:delete')")
    public CommonResult<Boolean> deleteYardListList(@RequestParam("ids") List<Long> ids) {
        yardListService.deleteYardListListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得CRM码单主")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:query')")
    public CommonResult<YardListRespVO> getYardList(@RequestParam("id") Long id) {
        YardListDO yardList = yardListService.getYardList(id);
        return success(BeanUtils.toBean(yardList, YardListRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得CRM码单主分页")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:query')")
    public CommonResult<PageResult<YardListRespVO>> getYardListPage(@Valid YardListPageReqVO pageReqVO) {
        PageResult<YardListDO> pageResult = yardListService.getYardListPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, YardListRespVO.class));
    }

    @GetMapping("/export-excel/{id}")
    @Operation(summary = "导出单个码单 Excel")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportYardListExcel(@PathVariable("id") Long id,
              HttpServletResponse response) throws IOException {
        // 获取单个码单
        YardListDO yardList = yardListService.getYardList(id);
        if (yardList == null) {
            throw new RuntimeException("码单不存在");
        }

        // 导出单个码单
        List<YardListDO> list = Collections.singletonList(yardList);
        yardListService.exportYardListExcel(list, response);
    }

    // ==================== 子表（CRM码单子表（款号包号详情）） ====================

    @GetMapping("/yard-list-detail/list-by-yard-list-id")
    @Operation(summary = "获得CRM码单子表（款号包号详情）列表")
    @Parameter(name = "yardListId", description = "码单主表ID")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:query')")
    public CommonResult<List<YardListDetailDO>> getYardListDetailListByYardListId(@RequestParam("yardListId") Long yardListId) {
        return success(yardListService.getYardListDetailListByYardListId(yardListId));
    }

    // ======================= 权限相关 =======================

    @GetMapping("/get-permission")
    @Operation(summary = "获得CRM码单的数据权限")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:yard-list:query')")
    public CommonResult<List<CrmPermissionRespVO>> getYardListPermissionList(@RequestParam("id") Long id) {
        List<CrmPermissionDO> result = permissionService.getPermissionListByBiz(CrmBizTypeEnum.CRM_YARD_LIST.getType(), id);
        return success(BeanUtils.toBean(result, CrmPermissionRespVO.class));
    }

}