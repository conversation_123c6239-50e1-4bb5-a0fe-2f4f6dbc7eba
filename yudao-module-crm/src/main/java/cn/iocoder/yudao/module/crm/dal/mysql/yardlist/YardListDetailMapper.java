package cn.iocoder.yudao.module.crm.dal.mysql.yardlist;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * CRM码单子表（款号包号详情） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YardListDetailMapper extends BaseMapperX<YardListDetailDO> {

    default List<YardListDetailDO> selectListByYardListId(Long yardListId) {
        return selectList(YardListDetailDO::getYardListId, yardListId);
    }

    default int deleteByYardListId(Long yardListId) {
        return delete(YardListDetailDO::getYardListId, yardListId);
    }

	default int deleteByYardListIds(List<Long> yardListIds) {
	    return deleteBatch(YardListDetailDO::getYardListId, yardListIds);
	}

}
