package cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - CRM结算单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SettlementRecordRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28453")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "结算日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结算日期")
    private LocalDate settlementDate;

    @Schema(description = "合同号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合同号")
    private String contractNo;

    @Schema(description = "品名款号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("品名款号")
    private String productNameCode;

    @Schema(description = "成分", example = "100%")
    @ExcelProperty("成分")
    private String composition;

    @Schema(description = "规格", example = "20*30")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "克重")
    @ExcelProperty("克重")
    private BigDecimal weight;

    @Schema(description = "幅宽")
    @ExcelProperty("幅宽")
    private String width;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @Schema(description = "单位", example = "1")
    @ExcelProperty(value = "单位", converter = DictConvert.class)
    @DictFormat("crm_product_unit") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer unit;

    @Schema(description = "单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1384")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额")
    private BigDecimal totalAmount;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "负责人的用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long ownerUserId;
    @Schema(description = "负责人名称", example = "芦道")
    @ExcelProperty("负责人")
    private String ownerUserName;

    @Schema(description = "客户编号", example = "1")
    private Long customerId;
    @Schema(description = "客户名称", example = "芦道")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long contractId;
    @Schema(description = "合同名称", example = "芦道")
    @ExcelProperty("合同名称")
    private String contractName;

    @Schema(description = "创建者")
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}