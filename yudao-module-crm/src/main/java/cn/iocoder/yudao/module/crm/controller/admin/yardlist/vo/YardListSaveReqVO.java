package cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDetailDO;

@Schema(description = "管理后台 - CRM码单主新增/修改 Request VO")
@Data
public class YardListSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27729")
    private Long id;

    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12257")
    @NotNull(message = "合同编号不能为空")
    private Long contractId;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19496")
    @NotNull(message = "客户编号不能为空")
    private Long customerId;

    @Schema(description = "负责人的用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14220")
    @NotNull(message = "负责人的用户编号不能为空")
    private Long ownerUserId;

    @Schema(description = "发货日期")
    private LocalDate deliveryDate;

    @Schema(description = "货品名称", example = "芋艿")
    private String productName;

    @Schema(description = "总匹数")
    private Integer totalPieces;

    @Schema(description = "总米数")
    private BigDecimal totalMeters;

    @Schema(description = "签收人")
    private String recipient;

    @Schema(description = "单位", example = "1")
    private Integer unit;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "CRM码单子表（款号包号详情）列表")
    private List<YardListDetailDO> yardListDetails;

}