package cn.iocoder.yudao.module.crm.dal.mysql.settlementrecord;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.crm.dal.dataobject.settlementrecord.SettlementRecordDO;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.util.CrmPermissionUtils;

import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo.*;

/**
 * CRM结算单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SettlementRecordMapper extends BaseMapperX<SettlementRecordDO> {

    default PageResult<SettlementRecordDO> selectPage(SettlementRecordPageReqVO reqVO) {
        return selectPage(reqVO, reqVO.getUserId(), reqVO.getSceneType());
    }

    default PageResult<SettlementRecordDO> selectPage(SettlementRecordPageReqVO reqVO, Long userId, Integer sceneType) {
        MPJLambdaWrapperX<SettlementRecordDO> query = new MPJLambdaWrapperX<>();

        // 如果没有指定场景类型，默认显示所有有权限的数据
        if (sceneType == null) {
            // 特殊情况：如果指定了合同ID，说明是在合同详情页面查看结算单，显示该合同的所有结算单
            if (reqVO.getContractId() != null) {
                // 在合同详情页面中，不应用权限过滤，显示该合同的所有结算单
                // 这里不添加额外的权限条件，只通过合同ID过滤
            } else {
                // 不是CRM管理员的情况下，需要过滤数据权限
                if (!CrmPermissionUtils.isCrmAdmin()) {
                    // 查询用户有权限的结算单ID（包括已删除负责人的结算单）
                    query.apply("(owner_user_id = {0} " + // 自己负责的
                            "OR EXISTS (SELECT 1 FROM crm_permission p WHERE p.biz_type = {1} AND p.biz_id = id AND p.user_id = {2}) " + // 自己参与的
                            "OR owner_user_id IN (" + // 下属负责的（包括已删除的下属）
                                "SELECT u.id FROM system_users u " +
                                "INNER JOIN system_dept d ON u.dept_id = d.id " +
                                "WHERE d.leader_user_id = {3} OR d.parent_id IN (" +
                                    "SELECT d2.id FROM system_dept d2 WHERE d2.leader_user_id = {4}" +
                                ")" +
                            "))",
                            userId, CrmBizTypeEnum.CRM_SETTLEMENT_RECORD.getType(), userId, userId, userId);
                }
            }
        } else {
            // 拼接数据权限的查询条件
            CrmPermissionUtils.appendPermissionCondition(query, CrmBizTypeEnum.CRM_SETTLEMENT_RECORD.getType(),
                    SettlementRecordDO::getId, userId, sceneType);
        }

        // 拼接自身的查询条件
        query.selectAll(SettlementRecordDO.class)
                .betweenIfPresent(SettlementRecordDO::getSettlementDate, reqVO.getSettlementDate())
                .eqIfPresent(SettlementRecordDO::getContractId, reqVO.getContractId());

        // 如果有合同编号搜索条件，需要关联查询
        if (StrUtil.isNotBlank(reqVO.getContractNo())) {
            query.apply("EXISTS (SELECT 1 FROM crm_contract c WHERE c.id = contract_id AND c.no LIKE {0})",
                    "%" + reqVO.getContractNo() + "%");
        }

        return selectJoinPage(reqVO, SettlementRecordDO.class, query.orderByDesc(SettlementRecordDO::getId));
    }

}