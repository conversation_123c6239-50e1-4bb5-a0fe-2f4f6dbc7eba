package cn.iocoder.yudao.module.crm.service.contract;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.crm.controller.admin.contract.vo.contract.CrmContractPageReqVO;
import cn.iocoder.yudao.module.crm.controller.admin.contract.vo.contract.CrmContractSaveReqVO;
import cn.iocoder.yudao.module.crm.controller.admin.contract.vo.contract.CrmContractTransferReqVO;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractConfigDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractProductDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.customer.CrmCustomerDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.product.CrmProductDO;
import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import cn.iocoder.yudao.module.crm.dal.mysql.contract.CrmContractMapper;
import cn.iocoder.yudao.module.crm.dal.mysql.contract.CrmContractProductMapper;
import cn.iocoder.yudao.module.crm.dal.redis.no.CrmNoRedisDAO;
import cn.iocoder.yudao.module.crm.enums.common.CrmAuditStatusEnum;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.enums.permission.CrmPermissionLevelEnum;
import cn.iocoder.yudao.module.crm.framework.permission.core.annotations.CrmPermission;
import cn.iocoder.yudao.module.crm.service.business.CrmBusinessService;
import cn.iocoder.yudao.module.crm.service.contact.CrmContactService;
import cn.iocoder.yudao.module.crm.service.customer.CrmCustomerService;
import cn.iocoder.yudao.module.crm.service.permission.CrmPermissionService;
import cn.iocoder.yudao.module.crm.service.permission.bo.CrmPermissionCreateReqBO;
import cn.iocoder.yudao.module.crm.service.permission.bo.CrmPermissionTransferReqBO;
import cn.iocoder.yudao.module.crm.service.product.CrmProductService;
import cn.iocoder.yudao.module.crm.service.receivable.CrmReceivableService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.module.crm.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.crm.enums.LogRecordConstants.*;
import static cn.iocoder.yudao.module.crm.util.CrmAuditStatusUtils.convertBpmResultToAuditStatus;

/**
 * CRM 合同 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CrmContractServiceImpl implements CrmContractService {

    /**
     * BPM 合同审批流程标识
     */
    public static final String BPM_PROCESS_DEFINITION_KEY = "crm-contract-audit";

    @Resource
    private CrmContractMapper contractMapper;
    @Resource
    private CrmContractProductMapper contractProductMapper;

    @Resource
    private CrmNoRedisDAO noRedisDAO;

    @Resource
    private CrmPermissionService crmPermissionService;
    @Resource
    private CrmProductService productService;
    @Resource
    private CrmCustomerService customerService;
    @Resource
    private CrmBusinessService businessService;
    @Resource
    private CrmContactService contactService;
    @Resource
    private CrmContractConfigService contractConfigService;
    @Resource
    @Lazy // 延迟加载，避免循环依赖
    private CrmReceivableService receivableService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private BpmProcessInstanceApi bpmProcessInstanceApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_CREATE_SUB_TYPE, bizNo = "{{#contract.id}}",
            success = CRM_CONTRACT_CREATE_SUCCESS)
    public Long createContract(CrmContractSaveReqVO createReqVO, Long userId) {
        // 1.1 校验产品项的有效性
        List<CrmContractProductDO> contractProducts = validateContractProducts(createReqVO.getProducts());
        // 1.2 校验关联字段
        validateRelationDataExists(createReqVO);
        // 1.3 生成序号
        String no = noRedisDAO.generate(CrmNoRedisDAO.CONTRACT_NO_PREFIX);
        if (contractMapper.selectByNo(no) != null) {
            throw exception(CONTRACT_NO_EXISTS);
        }

        // 2.1 插入合同
        CrmContractDO contract = BeanUtils.toBean(createReqVO, CrmContractDO.class).setNo(no);
        calculateTotalPrice(contract, contractProducts);
        contractMapper.insert(contract);
        // 2.2 插入合同关联商品
        if (CollUtil.isNotEmpty(contractProducts)) {
            contractProducts.forEach(item -> item.setContractId(contract.getId()));
            contractProductMapper.insertBatch(contractProducts);
        }

        // 3. 创建数据权限
        crmPermissionService.createPermission(new CrmPermissionCreateReqBO().setUserId(contract.getOwnerUserId())
                .setBizType(CrmBizTypeEnum.CRM_CONTRACT.getType()).setBizId(contract.getId())
                .setLevel(CrmPermissionLevelEnum.OWNER.getLevel()));

        // 4. 记录操作日志上下文
        LogRecordContext.putVariable("contract", contract);
        return contract.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
            success = CRM_CONTRACT_UPDATE_SUCCESS)
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#updateReqVO.id", level = CrmPermissionLevelEnum.WRITE)
    public void updateContract(CrmContractSaveReqVO updateReqVO) {
        Assert.notNull(updateReqVO.getId(), "合同编号不能为空");
        updateReqVO.setOwnerUserId(null); // 不允许更新的字段
        // 1.1 校验存在
        CrmContractDO contract = validateContractExists(updateReqVO.getId());
        // 1.2 只有草稿、审批中，可以编辑；
        if (!ObjectUtils.equalsAny(contract.getAuditStatus(), CrmAuditStatusEnum.DRAFT.getStatus(),
                CrmAuditStatusEnum.PROCESS.getStatus())) {
            throw exception(CONTRACT_UPDATE_FAIL_NOT_DRAFT);
        }
        // 1.3 校验产品项的有效性
        List<CrmContractProductDO> contractProducts = validateContractProducts(updateReqVO.getProducts());
        // 1.4 校验关联字段
        validateRelationDataExists(updateReqVO);

        // 2.1 更新合同
        CrmContractDO updateObj = BeanUtils.toBean(updateReqVO, CrmContractDO.class);
        calculateTotalPrice(updateObj, contractProducts);
        contractMapper.updateById(updateObj);
        // 2.2 更新合同关联商品
        updateContractProduct(updateReqVO.getId(), contractProducts);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(contract, CrmContractSaveReqVO.class));
        LogRecordContext.putVariable("contractName", contract.getName());
    }

    private void updateContractProduct(Long id, List<CrmContractProductDO> newList) {
        List<CrmContractProductDO> oldList = contractProductMapper.selectListByContractId(id);
        List<List<CrmContractProductDO>> diffList = diffList(oldList, newList, // id 不同，就认为是不同的记录
                (oldVal, newVal) -> oldVal.getId().equals(newVal.getId()));
        if (CollUtil.isNotEmpty(diffList.get(0))) {
            diffList.get(0).forEach(o -> o.setContractId(id));
            contractProductMapper.insertBatch(diffList.get(0));
        }
        if (CollUtil.isNotEmpty(diffList.get(1))) {
            contractProductMapper.updateBatch(diffList.get(1));
        }
        if (CollUtil.isNotEmpty(diffList.get(2))) {
            contractProductMapper.deleteByIds(convertSet(diffList.get(2), CrmContractProductDO::getId));
        }
    }

    /**
     * 校验关联数据是否存在
     *
     * @param reqVO 请求
     */
    private void validateRelationDataExists(CrmContractSaveReqVO reqVO) {
        // 1. 校验客户
        if (reqVO.getCustomerId() != null) {
            customerService.validateCustomer(reqVO.getCustomerId());
        }
        // 2. 校验负责人
        if (reqVO.getOwnerUserId() != null) {
            adminUserApi.validateUser(reqVO.getOwnerUserId());
        }
        // 3. 如果有关联商机，则需要校验存在
        if (reqVO.getBusinessId() != null) {
            businessService.validateBusiness(reqVO.getBusinessId());
        }
        // 4. 校验签约相关字段
        if (reqVO.getSignContactId() != null) {
            contactService.validateContact(reqVO.getSignContactId());
        }
        if (reqVO.getSignUserId() != null) {
            adminUserApi.validateUser(reqVO.getSignUserId());
        }
    }

    private List<CrmContractProductDO> validateContractProducts(List<CrmContractSaveReqVO.Product> list) {
        // 1. 校验产品存在
        productService.validProductList(convertSet(list, CrmContractSaveReqVO.Product::getProductId));
        // 2. 转化为 CrmContractProductDO 列表
        return convertList(list, o -> BeanUtils.toBean(o, CrmContractProductDO.class,
                item -> item.setTotalPrice(MoneyUtils.priceMultiply(item.getContractPrice(), item.getCount()))));
    }

    private void calculateTotalPrice(CrmContractDO contract, List<CrmContractProductDO> contractProducts) {
        contract.setTotalProductPrice(getSumValue(contractProducts, CrmContractProductDO::getTotalPrice, BigDecimal::add, BigDecimal.ZERO));
        BigDecimal discountPrice = MoneyUtils.priceMultiplyPercent(contract.getTotalProductPrice(), contract.getDiscountPercent());
        contract.setTotalPrice(contract.getTotalProductPrice().subtract(discountPrice));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_DELETE_SUB_TYPE, bizNo = "{{#id}}",
            success = CRM_CONTRACT_DELETE_SUCCESS)
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#id", level = CrmPermissionLevelEnum.OWNER)
    public void deleteContract(Long id) {
        // 1.1 校验存在
        CrmContractDO contract = validateContractExists(id);
        // 1.2 如果被 CrmReceivableDO 所使用，则不允许删除
        if (receivableService.getReceivableCountByContractId(contract.getId()) > 0) {
            throw exception(CONTRACT_DELETE_FAIL);
        }

        // 2.1 删除合同
        contractMapper.deleteById(id);
        // 2.2 删除数据权限
        crmPermissionService.deletePermission(CrmBizTypeEnum.CRM_CONTRACT.getType(), id);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("contractName", contract.getName());
    }

    private CrmContractDO validateContractExists(Long id) {
        CrmContractDO contract = contractMapper.selectById(id);
        if (contract == null) {
            throw exception(CONTRACT_NOT_EXISTS);
        }
        return contract;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_TRANSFER_SUB_TYPE, bizNo = "{{#reqVO.id}}",
            success = CRM_CONTRACT_TRANSFER_SUCCESS)
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#reqVO.id", level = CrmPermissionLevelEnum.OWNER)
    public void transferContract(CrmContractTransferReqVO reqVO, Long userId) {
        // 1. 校验合同是否存在
        CrmContractDO contract = validateContractExists(reqVO.getId());

        // 2.1 数据权限转移
        crmPermissionService.transferPermission(new CrmPermissionTransferReqBO(userId, CrmBizTypeEnum.CRM_CONTRACT.getType(),
                reqVO.getId(), reqVO.getNewOwnerUserId(), reqVO.getOldOwnerPermissionLevel()));
        // 2.2 设置负责人
        contractMapper.updateById(new CrmContractDO().setId(reqVO.getId()).setOwnerUserId(reqVO.getNewOwnerUserId()));

        // 3. 记录转移日志
        LogRecordContext.putVariable("contract", contract);
    }

    @Override
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_FOLLOW_UP_SUB_TYPE, bizNo = "{{#id}}",
            success = CRM_CONTRACT_FOLLOW_UP_SUCCESS)
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#id", level = CrmPermissionLevelEnum.WRITE)
    public void updateContractFollowUp(Long id, LocalDateTime contactNextTime, String contactLastContent) {
        // 1. 校验存在
        CrmContractDO contract = validateContractExists(id);

        // 2. 更新联系人的跟进信息
        contractMapper.updateById(new CrmContractDO().setId(id).setContactLastTime(LocalDateTime.now()));

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("contractName", contract.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = CRM_CONTRACT_TYPE, subType = CRM_CONTRACT_SUBMIT_SUB_TYPE, bizNo = "{{#id}}",
            success = CRM_CONTRACT_SUBMIT_SUCCESS)
    public void submitContract(Long id, Long userId) {
        // 1. 校验合同是否在审批
        CrmContractDO contract = validateContractExists(id);
        if (ObjUtil.notEqual(contract.getAuditStatus(), CrmAuditStatusEnum.DRAFT.getStatus())) {
            throw exception(CONTRACT_SUBMIT_FAIL_NOT_DRAFT);
        }

        // 2. 创建合同审批流程实例
        String processInstanceId = bpmProcessInstanceApi.createProcessInstance(userId, new BpmProcessInstanceCreateReqDTO()
                .setProcessDefinitionKey(BPM_PROCESS_DEFINITION_KEY).setBusinessKey(String.valueOf(id)));

        // 3. 更新合同工作流编号
        contractMapper.updateById(new CrmContractDO().setId(id).setProcessInstanceId(processInstanceId)
                .setAuditStatus(CrmAuditStatusEnum.PROCESS.getStatus()));

        // 3. 记录日志
        LogRecordContext.putVariable("contractName", contract.getName());
    }

    @Override
    public void updateContractAuditStatus(Long id, Integer bpmResult) {
        // 1.1 校验合同是否存在
        CrmContractDO contract = validateContractExists(id);
        // 1.2 只有审批中，可以更新审批结果
        if (ObjUtil.notEqual(contract.getAuditStatus(), CrmAuditStatusEnum.PROCESS.getStatus())) {
            log.error("[updateContractAuditStatus][contract({}) 不处于审批中，无法更新审批结果({})]",
                    contract.getId(), bpmResult);
            throw exception(CONTRACT_UPDATE_AUDIT_STATUS_FAIL_NOT_PROCESS);
        }

        // 2. 更新合同审批结果
        Integer auditStatus = convertBpmResultToAuditStatus(bpmResult);
        contractMapper.updateById(new CrmContractDO().setId(id).setAuditStatus(auditStatus));
    }

    // ======================= 查询相关 =======================

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#id", level = CrmPermissionLevelEnum.READ)
    public CrmContractDO getContract(Long id) {
        return contractMapper.selectById(id);
    }

    @Override
    public CrmContractDO validateContract(Long id) {
        return validateContractExists(id);
    }

    @Override
    public List<CrmContractDO> getContractList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return contractMapper.selectByIds(ids);
    }

    @Override
    public PageResult<CrmContractDO> getContractPage(CrmContractPageReqVO pageReqVO, Long userId) {
        // 1. 即将到期，需要查询合同配置
        CrmContractConfigDO config = null;
        if (CrmContractPageReqVO.EXPIRY_TYPE_ABOUT_TO_EXPIRE.equals(pageReqVO.getExpiryType())) {
            config = contractConfigService.getContractConfig();
            if (config != null && Boolean.FALSE.equals(config.getNotifyEnabled())) {
                config = null;
            }
            if (config == null) {
                return PageResult.empty();
            }
        }
        // 2. 查询分页
        return contractMapper.selectPage(pageReqVO, userId, config);
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CUSTOMER, bizId = "#pageReqVO.customerId", level = CrmPermissionLevelEnum.READ)
    public PageResult<CrmContractDO> getContractPageByCustomerId(CrmContractPageReqVO pageReqVO) {
        return contractMapper.selectPageByCustomerId(pageReqVO);
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_BUSINESS, bizId = "#pageReqVO.businessId", level = CrmPermissionLevelEnum.READ)
    public PageResult<CrmContractDO> getContractPageByBusinessId(CrmContractPageReqVO pageReqVO) {
        return contractMapper.selectPageByBusinessId(pageReqVO);
    }

    @Override
    public Long getContractCountByContactId(Long contactId) {
        return contractMapper.selectCountByContactId(contactId);
    }

    @Override
    public Long getContractCountByCustomerId(Long customerId) {
        return contractMapper.selectCount(CrmContractDO::getCustomerId, customerId);
    }

    @Override
    public Long getContractCountByBusinessId(Long businessId) {
        return contractMapper.selectCountByBusinessId(businessId);
    }

    @Override
    public List<CrmContractProductDO> getContractProductListByContractId(Long contactId) {
        return contractProductMapper.selectListByContractId(contactId);
    }

    @Override
    public Long getAuditContractCount(Long userId) {
        return contractMapper.selectCountByAudit(userId);
    }

    @Override
    public Long getRemindContractCount(Long userId) {
        CrmContractConfigDO config = contractConfigService.getContractConfig();
        if (config == null || Boolean.FALSE.equals(config.getNotifyEnabled())) {
            return 0L;
        }
        return contractMapper.selectCountByRemind(userId, config);
    }

    @Override
    public List<CrmContractDO> getContractListByCustomerIdOwnerUserId(Long customerId, Long ownerUserId) {
        return contractMapper.selectListByCustomerIdOwnerUserId(customerId, ownerUserId);
    }

    @Override
    @CrmPermission(bizType = CrmBizTypeEnum.CRM_CONTRACT, bizId = "#id", level = CrmPermissionLevelEnum.READ)
    public void exportContractDetail(Long id, HttpServletResponse response) throws IOException {
        try {
            log.info("开始导出合同详情，合同ID: {}", id);

            // 1. 获取合同信息
            CrmContractDO contract = validateContractExists(id);

            // 2. 获取合同产品列表
            List<CrmContractProductDO> contractProducts = contractProductMapper.selectListByContractId(id);

            // 3. 获取产品详细信息
            Map<Long, CrmProductDO> productMap = productService.getProductMap(
                    convertSet(contractProducts, CrmContractProductDO::getProductId));

            // 4. 获取客户信息
            String customerName = "";
            if (contract.getCustomerId() != null) {
                try {
                    CrmCustomerDO customer = customerService.getCustomer(contract.getCustomerId());
                    customerName = customer != null ? customer.getName() : "";
                } catch (Exception e) {
                    log.warn("获取客户信息失败，客户ID: {}", contract.getCustomerId(), e);
                    customerName = "";
                }
            }

            // 5. 读取模板文件
            ClassPathResource resource = new ClassPathResource("excel/contract_detail_template.xlsx");
            try (InputStream inputStream = resource.getInputStream();
                 Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 6. 填充固定数据
            // 第7行I列：合同编号
            Row row7 = sheet.getRow(6); // 第7行，索引为6
            if (row7 == null) row7 = sheet.createRow(6);
            Cell cellI7 = row7.getCell(8); // I列，索引为8
            if (cellI7 == null) cellI7 = row7.createCell(8);
            cellI7.setCellValue(contract.getNo());

            // 第9行C列：客户名称
            Row row9 = sheet.getRow(8); // 第9行，索引为8
            if (row9 == null) row9 = sheet.createRow(8);
            Cell cellC9 = row9.getCell(2); // C列，索引为2
            if (cellC9 == null) cellC9 = row9.createCell(2);
            cellC9.setCellValue(customerName);

            // 7. 计算需要的行数
            int startRowIndex = 13; // 第14行，索引为13
            int productRowCount = contractProducts.size();
            int totalRowIndex = startRowIndex + productRowCount; // 总金额行
            int chineseAmountRowIndex = totalRowIndex + 1; // 人民币大写金额行
            int templateBottomStartIndex = 18; // 模板第19行，索引为18（因为删除了更多行）
            int templateBottomEndIndex = 29; // 模板第30行，索引为29

            // 8. 清除可能存在的模板产品行（第14-18行）和相关合并区域，避免格式冲突
            log.info("开始清除模板行和合并区域");

            // 先删除涉及第14-18行的所有合并区域
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                int firstRow = mergedRegion.getFirstRow();
                int lastRow = mergedRegion.getLastRow();

                // 如果合并区域涉及第14-18行（索引13-17），则删除该合并区域
                if ((firstRow >= 13 && firstRow <= 17) || (lastRow >= 13 && lastRow <= 17) ||
                    (firstRow < 13 && lastRow > 17)) {
                    log.info("删除合并区域: 第{}行到第{}行, 第{}列到第{}列",
                        firstRow + 1, lastRow + 1, mergedRegion.getFirstColumn() + 1, mergedRegion.getLastColumn() + 1);
                    sheet.removeMergedRegion(i);
                }
            }

            // 再删除行对象
            for (int i = 13; i <= 17; i++) { // 第14-18行，索引13-17
                Row existingRow = sheet.getRow(i);
                if (existingRow != null) {
                    log.info("删除第{}行", i + 1);
                    sheet.removeRow(existingRow);
                }
            }

            log.info("模板行和合并区域清除完成");

            // 9. 先向下移动现有行，为新内容腾出空间
            int rowsToInsert = productRowCount + 2 - 4; // 需要插入的行数（产品行数+总金额行+人民币行-原有的3行模板行）
            log.info("计算行数：产品数量={}, 需要插入行数={}, 模板底部开始行={}", productRowCount, rowsToInsert, templateBottomStartIndex);

            if (rowsToInsert > 0) {
                try {
                    log.info("开始移动行：从第{}行到第{}行，向下移动{}行", templateBottomStartIndex + 1, sheet.getLastRowNum() + 1, rowsToInsert);
                    sheet.shiftRows(templateBottomStartIndex, sheet.getLastRowNum(), rowsToInsert);
                    log.info("行移动完成");
                } catch (Exception e) {
                    log.error("移动行失败", e);
                    throw e;
                }
            }

            // 10. 创建字体样式
            Font normalFont = workbook.createFont();
            normalFont.setFontHeightInPoints((short) 11); // 字体大小11pt

            // 创建边框样式
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setAlignment(HorizontalAlignment.CENTER);
            borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            borderStyle.setFont(normalFont); // 应用字体

            // 创建人民币大写行的特殊样式
            CellStyle chineseAmountStyle = workbook.createCellStyle();
            chineseAmountStyle.setBorderTop(BorderStyle.THIN);
            chineseAmountStyle.setBorderBottom(BorderStyle.THIN);
            chineseAmountStyle.setBorderLeft(BorderStyle.THIN);
            chineseAmountStyle.setBorderRight(BorderStyle.THIN);
            chineseAmountStyle.setAlignment(HorizontalAlignment.LEFT); // 居左显示
            chineseAmountStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建字体样式（加粗、增大）
            Font chineseAmountFont = workbook.createFont();
            chineseAmountFont.setBold(true); // 加粗
            chineseAmountFont.setFontHeightInPoints((short) 12); // 字体大小12pt
            chineseAmountStyle.setFont(chineseAmountFont);

            // 11. 填充产品数据（从第14行开始）
            BigDecimal totalAmount = BigDecimal.ZERO;
            log.info("开始填充产品数据，产品数量: {}", contractProducts.size());

            for (int i = 0; i < contractProducts.size(); i++) {
                try {
                    CrmContractProductDO contractProduct = contractProducts.get(i);
                    CrmProductDO product = productMap.get(contractProduct.getProductId());

                    // 创建新的产品行
                    int currentRowIndex = startRowIndex + i;
                    log.debug("创建产品行 {}, 行索引: {}", i + 1, currentRowIndex);
                    Row productRow = sheet.createRow(currentRowIndex);

                    // 设置行高为35磅（35 * 20 = 700，因为POI使用1/20磅作为单位）
                    productRow.setHeight((short) (35 * 20));

                    // B列：序号
                    Cell cellB = productRow.createCell(1);
                    cellB.setCellValue(i + 1);
                    cellB.setCellStyle(borderStyle);

                    // C列：产品品名
                    Cell cellC = productRow.createCell(2);
                    cellC.setCellValue(product != null ? product.getName() : "");
                    cellC.setCellStyle(borderStyle);

                    // D列：规格颜色
                    Cell cellD = productRow.createCell(3);
                    cellD.setCellValue(product != null && product.getSpecification() != null ? product.getSpecification() : "");
                    cellD.setCellStyle(borderStyle);

                    // E列：有效幅宽
                    Cell cellE = productRow.createCell(4);
                    cellE.setCellValue(product != null && product.getEffectiveWidth() != null ? product.getEffectiveWidth() : "");
                    cellE.setCellStyle(borderStyle);

                    // F列：单价
                    Cell cellF = productRow.createCell(5);
                    BigDecimal unitPrice = contractProduct.getContractPrice() != null ? contractProduct.getContractPrice() : BigDecimal.ZERO;
                    cellF.setCellValue(unitPrice.doubleValue());
                    cellF.setCellStyle(borderStyle);

                    // G列：单位
                    Cell cellG = productRow.createCell(6);
                    // 使用字典转换单位显示
                    String unitLabel = "";
                    if (product != null && product.getUnit() != null) {
                        unitLabel = DictFrameworkUtils.parseDictDataLabel("crm_product_unit", product.getUnit().toString());
                        if (unitLabel == null) {
                            unitLabel = product.getUnit().toString();
                        }
                    }
                    cellG.setCellValue(unitLabel);
                    cellG.setCellStyle(borderStyle);

                    // H列：数量
                    Cell cellH = productRow.createCell(7);
                    cellH.setCellValue(contractProduct.getCount() != null ? contractProduct.getCount().doubleValue() : 0);
                    cellH.setCellStyle(borderStyle);

                    // I列：金额
                    Cell cellI = productRow.createCell(8);
                    BigDecimal amount = contractProduct.getTotalPrice() != null ? contractProduct.getTotalPrice() : BigDecimal.ZERO;
                    cellI.setCellValue(amount.doubleValue());
                    cellI.setCellStyle(borderStyle);

                    totalAmount = totalAmount.add(amount);
                } catch (Exception e) {
                    log.error("填充产品行 {} 失败", i + 1, e);
                    throw e;
                }
            }

            log.info("产品数据填充完成，总金额: {}", totalAmount);

            // 12. 添加总金额行
            log.info("创建总金额行，行索引: {}", totalRowIndex);
            Row totalRow = sheet.createRow(totalRowIndex);

            // 设置行高为35磅
            totalRow.setHeight((short) (35 * 20));

            // 先为所有单元格创建边框，再合并
            for (int col = 1; col <= 8; col++) {
                Cell cell = totalRow.createCell(col);
                cell.setCellStyle(borderStyle);
            }

            // 合并B-H列
            sheet.addMergedRegion(new CellRangeAddress(totalRowIndex, totalRowIndex, 1, 7));

            // 设置合并后的单元格内容
            Cell totalLabelCell = totalRow.getCell(1);
            totalLabelCell.setCellValue("合计");
            totalLabelCell.setCellStyle(borderStyle);

            // I列：总金额
            Cell totalAmountCell = totalRow.getCell(8);
            totalAmountCell.setCellValue(totalAmount.doubleValue());
            totalAmountCell.setCellStyle(borderStyle);

            // 13. 添加人民币大写金额行
            log.info("创建人民币大写金额行，行索引: {}", chineseAmountRowIndex);
            Row chineseAmountRow = sheet.createRow(chineseAmountRowIndex);

            // 设置行高为35磅
            chineseAmountRow.setHeight((short) (35 * 20));

            // 先为所有单元格创建边框，再合并
            for (int col = 1; col <= 8; col++) {
                Cell cell = chineseAmountRow.createCell(col);
                cell.setCellStyle(chineseAmountStyle);
            }

            // 合并B-I列
            sheet.addMergedRegion(new CellRangeAddress(chineseAmountRowIndex, chineseAmountRowIndex, 1, 8));

            // 设置合并后的单元格内容
            Cell chineseAmountCell = chineseAmountRow.getCell(1);
            chineseAmountCell.setCellValue("人民币大写金额：" + convertToChineseAmount(totalAmount));
            chineseAmountCell.setCellStyle(chineseAmountStyle); // 重新应用样式

                // 14. 设置响应头
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("utf-8");
                String fileName = "合同详情-" + contract.getName() + ".xlsx";
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + java.net.URLEncoder.encode(fileName, "UTF-8"));

                // 15. 输出文件
                workbook.write(response.getOutputStream());
            }

            log.info("合同详情导出成功，合同ID: {}", id);
        } catch (Exception e) {
            log.error("导出合同详情失败，合同ID: {}", id, e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            throw new RuntimeException("导出合同详情失败: " + errorMessage, e);
        }
    }

    /**
     * 将数字金额转换为人民币大写
     *
     * @param amount 金额
     * @return 人民币大写
     */
    private String convertToChineseAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return "零元整";
        }

        String[] units = {"", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟"};
        String[] nums = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};

        // 将金额转换为分，避免小数点问题
        long totalCents = amount.multiply(new BigDecimal("100")).longValue();
        long yuan = totalCents / 100;
        long jiao = (totalCents % 100) / 10;
        long fen = totalCents % 10;

        StringBuilder result = new StringBuilder();

        // 处理元的部分
        if (yuan == 0) {
            result.append("零");
        } else {
            String yuanStr = String.valueOf(yuan);
            int len = yuanStr.length();
            boolean hasZero = false;

            for (int i = 0; i < len; i++) {
                int digit = Integer.parseInt(yuanStr.substring(i, i + 1));
                int pos = len - i - 1;

                if (digit == 0) {
                    if (pos == 4 || pos == 8) { // 万位或亿位
                        if (result.length() > 0 && !result.toString().endsWith("万") && !result.toString().endsWith("亿")) {
                            result.append(units[pos]);
                        }
                    }
                    hasZero = true;
                } else {
                    if (hasZero && result.length() > 0) {
                        result.append("零");
                    }
                    result.append(nums[digit]);
                    if (pos > 0) {
                        result.append(units[pos]);
                    }
                    hasZero = false;
                }
            }
        }

        result.append("元");

        // 处理角分
        if (jiao == 0 && fen == 0) {
            result.append("整");
        } else {
            if (jiao != 0) {
                result.append(nums[(int) jiao]).append("角");
            }
            if (fen != 0) {
                if (jiao == 0 && yuan != 0) {
                    result.append("零");
                }
                result.append(nums[(int) fen]).append("分");
            }
        }

        return result.toString();
    }

    /**
     * 复制Excel行
     *
     * @param sourceRow 源行
     * @param newRow    新行
     */
    private void copyRow(Row sourceRow, Row newRow) {
        // 复制行高
        newRow.setHeight(sourceRow.getHeight());

        // 复制每个单元格
        for (int i = sourceRow.getFirstCellNum(); i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell newCell = newRow.createCell(i);
                copyCell(sourceCell, newCell);
            }
        }
    }

    /**
     * 复制Excel单元格
     *
     * @param sourceCell 源单元格
     * @param newCell    新单元格
     */
    private void copyCell(Cell sourceCell, Cell newCell) {
        // 复制单元格样式
        newCell.setCellStyle(sourceCell.getCellStyle());

        // 复制单元格值
        switch (sourceCell.getCellType()) {
            case STRING:
                newCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                newCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                newCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                newCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case BLANK:
                newCell.setBlank();
                break;
            default:
                break;
        }
    }

}
