package cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - CRM结算单新增/修改 Request VO")
@Data
public class SettlementRecordSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28453")
    private Long id;

    @Schema(description = "结算日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结算日期不能为空")
    private LocalDate settlementDate;

    @Schema(description = "品名款号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品名款号不能为空")
    private String productNameCode;

    @Schema(description = "成分", example = "100%")
    private String composition;

    @Schema(description = "规格", example = "20*30")
    private String specification;

    @Schema(description = "克重")
    private BigDecimal weight;

    @Schema(description = "幅宽")
    private String width;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    @Schema(description = "单位", example = "1")
    private Integer unit;

    @Schema(description = "单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1384")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "金额不能为空")
    private BigDecimal totalAmount;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "负责人编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "负责人编号不能为空")
    private Long ownerUserId;

    @Schema(description = "客户编号", example = "1")
    private Long customerId; // 该字段不通过前端传递，而是 contractId 查询出来设置进去

    @Schema(description = "合同ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "合同不能为空")
    private Long contractId;

}