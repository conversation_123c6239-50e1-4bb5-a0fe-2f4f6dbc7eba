package cn.iocoder.yudao.module.crm.controller.admin.settlementrecord;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.crm.controller.admin.settlementrecord.vo.*;
import cn.iocoder.yudao.module.crm.dal.dataobject.settlementrecord.SettlementRecordDO;
import cn.iocoder.yudao.module.crm.service.settlementrecord.SettlementRecordService;
import cn.iocoder.yudao.module.crm.dal.dataobject.permission.CrmPermissionDO;
import cn.iocoder.yudao.module.crm.service.permission.CrmPermissionService;
import cn.iocoder.yudao.module.crm.controller.admin.permission.vo.CrmPermissionRespVO;

import cn.iocoder.yudao.module.crm.service.permission.bo.CrmPermissionTransferReqBO;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.service.contract.CrmContractService;
import cn.iocoder.yudao.module.crm.service.customer.CrmCustomerService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.customer.CrmCustomerDO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;

@Tag(name = "管理后台 - CRM结算单")
@RestController
@RequestMapping("/crm/settlement-record")
@Validated
@Slf4j
public class SettlementRecordController {

    @Resource
    private SettlementRecordService settlementRecordService;
    @Resource
    private CrmPermissionService permissionService;
    @Resource
    private CrmContractService contractService;
    @Resource
    private CrmCustomerService customerService;
    @Resource
    private AdminUserApi adminUserApi;

    @PostMapping("/create")
    @Operation(summary = "创建CRM结算单")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:create')")
    public CommonResult<Long> createSettlementRecord(@Valid @RequestBody SettlementRecordSaveReqVO createReqVO) {
        return success(settlementRecordService.createSettlementRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新CRM结算单")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:update')")
    public CommonResult<Boolean> updateSettlementRecord(@Valid @RequestBody SettlementRecordSaveReqVO updateReqVO) {
        settlementRecordService.updateSettlementRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除CRM结算单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:delete')")
    public CommonResult<Boolean> deleteSettlementRecord(@RequestParam("id") Long id) {
        settlementRecordService.deleteSettlementRecord(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除CRM结算单")
                @PreAuthorize("@ss.hasPermission('crm:settlement-record:delete')")
    public CommonResult<Boolean> deleteSettlementRecordList(@RequestParam("ids") List<Long> ids) {
        settlementRecordService.deleteSettlementRecordListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得CRM结算单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:query')")
    public CommonResult<SettlementRecordRespVO> getSettlementRecord(@RequestParam("id") Long id) {
        SettlementRecordDO settlementRecord = settlementRecordService.getSettlementRecord(id);
        return success(BeanUtils.toBean(settlementRecord, SettlementRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得CRM结算单分页")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:query')")
    public CommonResult<PageResult<SettlementRecordRespVO>> getSettlementRecordPage(@Valid SettlementRecordPageReqVO pageReqVO) {
        PageResult<SettlementRecordDO> pageResult = settlementRecordService.getSettlementRecordPage(pageReqVO);

        // 转换为响应VO并填充关联数据
        List<SettlementRecordRespVO> respList = BeanUtils.toBean(pageResult.getList(), SettlementRecordRespVO.class);

        // 填充关联数据
        respList.forEach(resp -> {
            // 先检查负责人是否已删除
            boolean isOwnerDeleted = false;
            if (resp.getOwnerUserId() != null) {
                try {
                    AdminUserRespDTO user = adminUserApi.getUser(resp.getOwnerUserId());
                    if (user != null) {
                        resp.setOwnerUserName(user.getNickname());
                    } else {
                        // 用户已被删除，显示提示信息
                        resp.setOwnerUserName("用户已删除(ID:" + resp.getOwnerUserId() + ")");
                        isOwnerDeleted = true;
                    }
                } catch (Exception e) {
                    // 异常情况，显示提示信息
                    resp.setOwnerUserName("用户信息异常(ID:" + resp.getOwnerUserId() + ")");
                    isOwnerDeleted = true;
                }
            }

            // 如果负责人已删除，跳过合同和客户信息获取，避免权限问题
            if (!isOwnerDeleted) {
                // 填充合同信息
                if (resp.getContractId() != null) {
                    try {
                        CrmContractDO contract = contractService.getContract(resp.getContractId());
                        if (contract != null) {
                            resp.setContractName(contract.getName());
                            resp.setContractNo(contract.getNo());
                        } else {
                            // 合同不存在或无权限访问
                            resp.setContractNo("无权限访问(ID:" + resp.getContractId() + ")");
                        }
                    } catch (Exception e) {
                        // 权限异常或其他异常，记录日志并显示提示
                        log.warn("获取合同信息失败，合同ID: {}, 错误: {}", resp.getContractId(), e.getMessage());
                        resp.setContractNo("无权限访问(ID:" + resp.getContractId() + ")");
                    }
                }

                // 填充客户信息
                if (resp.getCustomerId() != null) {
                    try {
                        CrmCustomerDO customer = customerService.getCustomer(resp.getCustomerId());
                        if (customer != null) {
                            resp.setCustomerName(customer.getName());
                        }
                    } catch (Exception e) {
                        // 忽略异常，继续处理
                    }
                }
            } else {
                // 负责人已删除，设置默认提示信息
                if (resp.getContractId() != null) {
                    resp.setContractNo("负责人已删除");
                }
                if (resp.getCustomerId() != null) {
                    resp.setCustomerName("负责人已删除");
                }
            }
        });

        return success(new PageResult<>(respList, pageResult.getTotal()));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出CRM结算单 Excel")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSettlementRecordExcel(@Valid SettlementRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SettlementRecordDO> list = settlementRecordService.getSettlementRecordPage(pageReqVO).getList();
        // 使用自定义模板导出 Excel
        settlementRecordService.exportSettlementRecordExcel(list, response);
    }

    // ======================= 权限相关 =======================

    @GetMapping("/get-permission")
    @Operation(summary = "获得CRM结算单的数据权限")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:query')")
    public CommonResult<List<CrmPermissionRespVO>> getSettlementRecordPermissionList(@RequestParam("id") Long id) {
        List<CrmPermissionDO> result = permissionService.getPermissionListByBiz(CrmBizTypeEnum.CRM_SETTLEMENT_RECORD.getType(), id);
        return success(BeanUtils.toBean(result, CrmPermissionRespVO.class));
    }

    @PutMapping("/update-permission")
    @Operation(summary = "编辑CRM结算单的数据权限")
    @PreAuthorize("@ss.hasPermission('crm:settlement-record:update')")
    public CommonResult<Boolean> updateSettlementRecordPermission(@Valid @RequestBody SettlementRecordTransferReqVO reqVO) {
        permissionService.transferPermission(
                new CrmPermissionTransferReqBO().setBizType(CrmBizTypeEnum.CRM_SETTLEMENT_RECORD.getType()).setBizId(reqVO.getId())
                        .setNewOwnerUserId(reqVO.getNewOwnerUserId()).setOldOwnerPermissionLevel(reqVO.getOldOwnerPermissionLevel())
                        .setUserId(getLoginUserId()));
        return success(true);
    }

}