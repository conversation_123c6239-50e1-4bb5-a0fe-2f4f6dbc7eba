package cn.iocoder.yudao.module.crm.service.yardlist;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

import java.util.*;
import java.util.stream.Collectors;
import cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo.*;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

// Excel相关导入
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;

// 日志
import lombok.extern.slf4j.Slf4j;

import cn.iocoder.yudao.module.crm.dal.mysql.yardlist.YardListMapper;
import cn.iocoder.yudao.module.crm.dal.mysql.yardlist.YardListDetailMapper;
import cn.iocoder.yudao.module.crm.dal.dataobject.contract.CrmContractDO;
import cn.iocoder.yudao.module.crm.dal.dataobject.customer.CrmCustomerDO;
import cn.iocoder.yudao.module.crm.service.contract.CrmContractService;
import cn.iocoder.yudao.module.crm.service.customer.CrmCustomerService;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.diffList;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.crm.enums.ErrorCodeConstants.*;

/**
 * CRM码单主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class YardListServiceImpl implements YardListService {

    @Resource
    private YardListMapper yardListMapper;
    @Resource
    private YardListDetailMapper yardListDetailMapper;

    // 添加其他服务的依赖
    @Resource
    private CrmContractService contractService;
    @Resource
    private CrmCustomerService customerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createYardList(YardListSaveReqVO createReqVO) {
        // 插入
        YardListDO yardList = BeanUtils.toBean(createReqVO, YardListDO.class);
        yardListMapper.insert(yardList);

        // 插入子表
        createYardListDetailList(yardList.getId(), createReqVO.getYardListDetails());
        // 返回
        return yardList.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateYardList(YardListSaveReqVO updateReqVO) {
        // 校验存在
        validateYardListExists(updateReqVO.getId());
        // 更新
        YardListDO updateObj = BeanUtils.toBean(updateReqVO, YardListDO.class);
        yardListMapper.updateById(updateObj);

        // 更新子表
        updateYardListDetailList(updateReqVO.getId(), updateReqVO.getYardListDetails());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteYardList(Long id) {
        // 校验存在
        validateYardListExists(id);
        // 删除
        yardListMapper.deleteById(id);

        // 删除子表
        deleteYardListDetailByYardListId(id);
    }

    @Override
        @Transactional(rollbackFor = Exception.class)
    public void deleteYardListListByIds(List<Long> ids) {
        // 校验存在
        validateYardListExists(ids);
        // 删除
        yardListMapper.deleteByIds(ids);
    
    // 删除子表
            deleteYardListDetailByYardListIds(ids);
    }

    private void validateYardListExists(List<Long> ids) {
        List<YardListDO> list = yardListMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(YARD_LIST_NOT_EXISTS);
        }
    }

    private void validateYardListExists(Long id) {
        if (yardListMapper.selectById(id) == null) {
            throw exception(YARD_LIST_NOT_EXISTS);
        }
    }

    @Override
    public YardListDO getYardList(Long id) {
        return yardListMapper.selectById(id);
    }

    @Override
    public PageResult<YardListDO> getYardListPage(YardListPageReqVO pageReqVO) {
        return yardListMapper.selectPage(pageReqVO, getLoginUserId(), pageReqVO.getSceneType());
    }

    // ==================== 子表（CRM码单子表（款号包号详情）） ====================

    @Override
    public List<YardListDetailDO> getYardListDetailListByYardListId(Long yardListId) {
        return yardListDetailMapper.selectListByYardListId(yardListId);
    }

    private void createYardListDetailList(Long yardListId, List<YardListDetailDO> list) {
        list.forEach(o -> o.setYardListId(yardListId).clean());
        yardListDetailMapper.insertBatch(list);
    }

    private void updateYardListDetailList(Long yardListId, List<YardListDetailDO> list) {
	    list.forEach(o -> o.setYardListId(yardListId).clean());
	    List<YardListDetailDO> oldList = yardListDetailMapper.selectListByYardListId(yardListId);
	    List<List<YardListDetailDO>> diffList = diffList(oldList, list, (oldVal, newVal) -> {
            boolean same = ObjectUtil.equal(oldVal.getId(), newVal.getId());
            if (same) {
                newVal.setId(oldVal.getId()).clean(); // 解决更新情况下：updateTime 不更新
            }
            return same;
	    });

	    // 第二步，批量添加、修改、删除
	    if (CollUtil.isNotEmpty(diffList.get(0))) {
	        yardListDetailMapper.insertBatch(diffList.get(0));
	    }
	    if (CollUtil.isNotEmpty(diffList.get(1))) {
	        yardListDetailMapper.updateBatch(diffList.get(1));
	    }
	    if (CollUtil.isNotEmpty(diffList.get(2))) {
	        yardListDetailMapper.deleteByIds(convertList(diffList.get(2), YardListDetailDO::getId));
	    }
    }

    private void deleteYardListDetailByYardListId(Long yardListId) {
        yardListDetailMapper.deleteByYardListId(yardListId);
    }

	private void deleteYardListDetailByYardListIds(List<Long> yardListIds) {
        yardListDetailMapper.deleteByYardListIds(yardListIds);
	}

    /**
     * 导出码单Excel（使用模板）
     */
    @Override
    public void exportYardListExcel(List<YardListDO> list, HttpServletResponse response) throws IOException {
        log.info("开始导出码单，数据条数：{}", list.size());

        try {
            // 1. 检查模板文件，如果不存在则使用简单导出
            InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("excel/yardlist_template.xlsx");
            if (templateStream == null) {
                log.warn("码单模板文件不存在，使用简单导出");
                // 使用简单的Excel导出作为临时方案
                exportYardListSimple(list, response);
                return;
            }

        log.info("模板文件读取成功，开始处理Excel");

        try (Workbook workbook = new XSSFWorkbook(templateStream)) {
            log.info("Excel工作簿创建成功");
            System.out.println("=== Excel工作簿创建成功 ===");

            Sheet sheet = workbook.getSheetAt(0);
            log.info("Excel工作表创建成功，工作表名：{}", sheet.getSheetName());
            System.out.println("=== Excel工作表创建成功，工作表名：" + sheet.getSheetName() + " ===");

            // 处理每个码单
            for (YardListDO yardList : list) {
                log.info("开始处理码单ID：{}", yardList.getId());
                System.out.println("=== 开始处理码单ID：" + yardList.getId() + " ===");
                processYardListSheet(workbook, sheet, yardList);
                log.info("码单处理完成");
                System.out.println("=== 码单处理完成 ===");
                break; // 目前只处理第一个码单，如果需要处理多个，需要创建多个工作表
            }

            // 输出文件
            String fileName = "码单_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            // 支持中文文件名
            try {
                String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            } catch (UnsupportedEncodingException e) {
                response.setHeader("Content-Disposition", "attachment; filename=yardlist.xlsx");
            }

            log.info("导出码单文件：{}", fileName);
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
            log.info("码单导出完成");
        } finally {
            IoUtil.close(templateStream);
        }
        } catch (Exception e) {
            log.error("导出码单Excel失败", e);
            throw new RuntimeException("导出码单Excel失败：" + e.getMessage(), e);
        }
    }

    /**
     * 简单导出码单Excel（当模板文件不存在时使用）
     */
    private void exportYardListSimple(List<YardListDO> list, HttpServletResponse response) throws IOException {
        log.info("使用简单方式导出码单");

        // 创建新的工作簿
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("码单");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"码单ID", "客户ID", "合同ID", "发货日期", "货品名称", "总匹数", "总数量", "签收人", "单位", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            int rowNum = 1;
            for (YardListDO yardList : list) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(yardList.getId() != null ? yardList.getId().toString() : "");
                row.createCell(1).setCellValue(yardList.getCustomerId() != null ? yardList.getCustomerId().toString() : "");
                row.createCell(2).setCellValue(yardList.getContractId() != null ? yardList.getContractId().toString() : "");
                row.createCell(3).setCellValue(yardList.getDeliveryDate() != null ? yardList.getDeliveryDate().toString() : "");
                row.createCell(4).setCellValue(yardList.getProductName() != null ? yardList.getProductName() : "");
                row.createCell(5).setCellValue(yardList.getTotalPieces() != null ? yardList.getTotalPieces().toString() : "0");
                row.createCell(6).setCellValue(yardList.getTotalMeters() != null ? yardList.getTotalMeters().toString() : "0");
                row.createCell(7).setCellValue(yardList.getRecipient() != null ? yardList.getRecipient() : "");
                row.createCell(8).setCellValue(getUnitText(yardList.getUnit()));
                row.createCell(9).setCellValue(yardList.getRemark() != null ? yardList.getRemark() : "");
            }

            // 设置响应头
            String fileName = "码单_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            try {
                String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            } catch (UnsupportedEncodingException e) {
                response.setHeader("Content-Disposition", "attachment; filename=yardlist.xlsx");
            }

            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
            log.info("简单导出完成");
        }
    }

    /**
     * 处理单个码单的工作表
     */
    private void processYardListSheet(Workbook workbook, Sheet sheet, YardListDO yardList) {
        log.info("开始处理码单工作表，码单ID：{}", yardList.getId());
        System.out.println("=== 开始处理码单工作表，码单ID：" + yardList.getId() + " ===");

        // 2. 获取合同和客户信息
        String contractNo = "";
        String customerName = "";
        String unitText = "单位";

        if (yardList.getContractId() != null) {
            try {
                log.info("获取合同信息，合同ID：{}", yardList.getContractId());
                System.out.println("=== 获取合同信息，合同ID：" + yardList.getContractId() + " ===");

                CrmContractDO contractInfo = contractService.getContract(yardList.getContractId());
                contractNo = contractInfo.getNo();
                log.info("获取到合同编号：{}", contractNo);
                System.out.println("=== 获取到合同编号：" + contractNo + " ===");

                if (contractInfo.getCustomerId() != null) {
                    log.info("获取客户信息，客户ID：{}", contractInfo.getCustomerId());
                    System.out.println("=== 获取客户信息，客户ID：" + contractInfo.getCustomerId() + " ===");

                    CrmCustomerDO customer = customerService.getCustomer(contractInfo.getCustomerId());
                    customerName = customer.getName();
                    log.info("获取到客户名称：{}", customerName);
                    System.out.println("=== 获取到客户名称：" + customerName + " ===");
                }
            } catch (Exception e) {
                log.warn("获取合同和客户信息失败", e);
                System.out.println("=== 获取合同和客户信息失败：" + e.getMessage() + " ===");
                e.printStackTrace();
            }
        }

        // 获取单位文本
        if (yardList.getUnit() != null) {
            unitText = getUnitText(yardList.getUnit());
        }

        // 3. 填充第7行信息
        Row row7 = sheet.getRow(6); // 第7行（索引从0开始）
        if (row7 != null) {
            // 创建样式
            CellStyle leftAlignStyle = workbook.createCellStyle();
            leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
            leftAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            CellStyle centerAlignStyle = workbook.createCellStyle();
            centerAlignStyle.setAlignment(HorizontalAlignment.CENTER);
            centerAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // BC合并列：客户单位（居左显示）
            Cell customerCell = createTextCell(row7, 1, "客户单位：" + customerName);
            customerCell.setCellStyle(leftAlignStyle);
            // 合并BC列
            try {
                sheet.addMergedRegion(new CellRangeAddress(6, 6, 1, 2));
            } catch (Exception e) {
                log.warn("合并客户单位BC列失败: {}", e.getMessage());
            }

            // DE合并列：合同编号（居左显示）
            Cell contractCell = createTextCell(row7, 3, "合同编号：" + contractNo);
            contractCell.setCellStyle(leftAlignStyle);
            // 合并DE列
            try {
                sheet.addMergedRegion(new CellRangeAddress(6, 6, 3, 4));
            } catch (Exception e) {
                log.warn("合并合同编号DE列失败: {}", e.getMessage());
            }

            // F列：货品（居中显示）
            Cell productCell = createTextCell(row7, 5, "货品：" + (yardList.getProductName() != null ? yardList.getProductName() : ""));
            productCell.setCellStyle(centerAlignStyle);
        }

        // 4. 处理第8行表头，修改D列为包号
        Row row8 = sheet.getRow(7); // 第8行（表头行）
        if (row8 != null) {
            // 获取原有表头的样式
            CellStyle headerStyle = null;
            if (row8.getCell(1) != null) {
                headerStyle = row8.getCell(1).getCellStyle();
            }

            // B列：序号，C列：款号，D列：包号，E列：数量（单位），F列：备注
            createStyledTextCell(row8, 1, "序号", headerStyle);
            createStyledTextCell(row8, 2, "款号", headerStyle);
            createStyledTextCell(row8, 3, "包号", headerStyle);
            createStyledTextCell(row8, 4, "数量（" + unitText + "）", headerStyle);
            createStyledTextCell(row8, 5, "备注", headerStyle);
        }

        // 5. 获取码单详情数据
        List<YardListDetailDO> details = yardListDetailMapper.selectListByYardListId(yardList.getId());

        // 6. 清除第9行以下的所有模板数据
        clearRowsFromIndex(sheet, 8); // 从第9行开始清除所有内容

        // 7. 重新填充所有数据
        fillCompleteYardListData(sheet, details, yardList, unitText);
    }

    /**
     * 清除指定行索引以下的所有行
     */
    private void clearRowsFromIndex(Sheet sheet, int startRowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        for (int i = lastRowNum; i >= startRowIndex; i--) {
            Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
    }

    /**
     * 填充完整的码单数据
     */
    private void fillCompleteYardListData(Sheet sheet, List<YardListDetailDO> details, YardListDO yardList, String unitText) {
        int currentRowIndex = 8; // 从第9行开始（索引8）

        // 1. 填充详情数据
        currentRowIndex = fillDetailData(sheet, details, currentRowIndex);

        // 2. 空一行
        currentRowIndex++;

        // 3. 填充款号汇总
        currentRowIndex = fillStyleSummaryNew(sheet, details, currentRowIndex, unitText);

        // 4. 空一行
        currentRowIndex++;

        // 5. 填充总计
        currentRowIndex = fillGrandTotalNew(sheet, details, currentRowIndex, unitText);

        // 6. 空两行后填充发货信息
        currentRowIndex += 2;
        fillDeliveryInfoNew(sheet, yardList, currentRowIndex);
    }

    /**
     * 填充详情数据并添加边框
     */
    private int fillDetailData(Sheet sheet, List<YardListDetailDO> details, int startRowIndex) {
        int currentRowIndex = startRowIndex;
        int sequenceNo = 1;

        // 创建居中对齐样式
        Workbook workbook = sheet.getWorkbook();
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 填充每一行详情数据
        for (YardListDetailDO detail : details) {
            Row dataRow = sheet.createRow(currentRowIndex++);

            // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
            dataRow.setHeight((short) (25 * 20));

            // B列：序号（居中，数值格式）
            createStyledNumberCell(dataRow, 1, sequenceNo++, centerStyle);
            // C列：款号（居中）
            createStyledTextCell(dataRow, 2, detail.getStyleNo() != null ? detail.getStyleNo() : "", centerStyle);
            // D列：包号（居中，数值格式）
            Integer packageNo = null;
            if (detail.getPackageNo() != null && !detail.getPackageNo().trim().isEmpty()) {
                try {
                    packageNo = Integer.parseInt(detail.getPackageNo().trim());
                } catch (NumberFormatException e) {
                    // 如果包号不是纯数字，则使用文本格式
                    createStyledTextCell(dataRow, 3, detail.getPackageNo(), centerStyle);
                    packageNo = null;
                }
            }
            if (packageNo != null) {
                createStyledNumberCell(dataRow, 3, packageNo, centerStyle);
            } else if (packageNo == null && (detail.getPackageNo() == null || detail.getPackageNo().trim().isEmpty())) {
                createStyledTextCell(dataRow, 3, "", centerStyle);
            }

            // E列：数量（居中，数值格式）
            Integer quantity = 0;
            if (detail.getQuantity() != null) {
                quantity = detail.getQuantity().intValue(); // 转为整数
            }
            createStyledNumberCell(dataRow, 4, quantity, centerStyle);
            // F列：备注（居中）
            createStyledTextCell(dataRow, 5, "", centerStyle);
        }

        // 添加表格边框（B列到F列）
        addTableBorders(sheet, startRowIndex, currentRowIndex - 1, 1, 5);

        return currentRowIndex;
    }

    /**
     * 填充款号汇总（新版本）
     */
    private int fillStyleSummaryNew(Sheet sheet, List<YardListDetailDO> details, int startRowIndex, String unitText) {
        // 按款号分组统计
        Map<String, List<YardListDetailDO>> styleGroups = new LinkedHashMap<>();
        Map<String, Integer> styleCountMap = new HashMap<>();
        Map<String, BigDecimal> styleTotalMap = new HashMap<>();

        for (YardListDetailDO detail : details) {
            String styleNo = detail.getStyleNo() != null ? detail.getStyleNo() : "";
            styleGroups.computeIfAbsent(styleNo, k -> new ArrayList<>()).add(detail);

            // 统计计次
            styleCountMap.put(styleNo, styleCountMap.getOrDefault(styleNo, 0) + 1);

            // 统计总数量
            BigDecimal quantity = detail.getQuantity() != null ? detail.getQuantity() : BigDecimal.ZERO;
            styleTotalMap.put(styleNo, styleTotalMap.getOrDefault(styleNo, BigDecimal.ZERO).add(quantity));
        }

        int currentRowIndex = startRowIndex;
        int styleIndex = 1;

        // 创建居中对齐样式
        Workbook workbook = sheet.getWorkbook();
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 填充每个款号的汇总信息
        for (Map.Entry<String, List<YardListDetailDO>> entry : styleGroups.entrySet()) {
            String styleNo = entry.getKey();

            // 上一行：款号序号 + 款号名称 + 计次 + 空 + 匹
            Row row1 = sheet.createRow(currentRowIndex++);

            // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
            row1.setHeight((short) (25 * 20));

            createStyledTextCell(row1, 1, "款号" + styleIndex, centerStyle); // B列居中
            createStyledTextCell(row1, 2, styleNo, centerStyle); // C列居中
            createStyledNumberCell(row1, 3, styleCountMap.get(styleNo), centerStyle); // D列居中，数值格式
            createStyledTextCell(row1, 4, "匹", centerStyle); // E列居中，匹
            createStyledTextCell(row1, 5, "", centerStyle); // F列居中

            // 下一行：空 + 空 + 总数量 + 单位 + 空
            Row row2 = sheet.createRow(currentRowIndex++);

            // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
            row2.setHeight((short) (25 * 20));

            createStyledTextCell(row2, 1, "", centerStyle); // B列居中
            createStyledTextCell(row2, 2, "", centerStyle); // C列居中
            // 总数量转为整数，数值格式
            Integer totalQuantity = styleTotalMap.get(styleNo).intValue();
            createStyledNumberCell(row2, 3, totalQuantity, centerStyle); // D列居中，数值格式
            createStyledTextCell(row2, 4, unitText, centerStyle); // E列居中，单位
            createStyledTextCell(row2, 5, "", centerStyle); // F列居中

            styleIndex++;
        }

        // 添加表格边框（B-F列）
        if (currentRowIndex > startRowIndex) {
            addTableBorders(sheet, startRowIndex, currentRowIndex - 1, 1, 5);
        }

        return currentRowIndex;
    }

    /**
     * 填充总计（新版本）
     */
    private int fillGrandTotalNew(Sheet sheet, List<YardListDetailDO> details, int startRowIndex, String unitText) {
        // 计算总计次和总数量
        int totalCount = details.size();
        BigDecimal totalQuantity = details.stream()
            .map(detail -> detail.getQuantity() != null ? detail.getQuantity() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        int currentRowIndex = startRowIndex;

        // 创建居中对齐样式
        Workbook workbook = sheet.getWorkbook();
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 上一行：合计（BC合并） + 总计次 + 匹 + 空
        Row row1 = sheet.createRow(currentRowIndex++);

        // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
        row1.setHeight((short) (25 * 20));

        createStyledTextCell(row1, 1, "合计", centerStyle);
        createStyledTextCell(row1, 2, "", centerStyle);
        createStyledNumberCell(row1, 3, totalCount, centerStyle); // D列居中，数值格式
        createStyledTextCell(row1, 4, "匹", centerStyle); // E列居中，匹
        createStyledTextCell(row1, 5, "", centerStyle); // F列居中

        // 下一行：空（BC合并） + 总数量 + 单位 + 空
        Row row2 = sheet.createRow(currentRowIndex++);

        // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
        row2.setHeight((short) (25 * 20));

        createStyledTextCell(row2, 1, "", centerStyle);
        createStyledTextCell(row2, 2, "", centerStyle);
        createStyledNumberCell(row2, 3, totalQuantity.intValue(), centerStyle); // D列居中，数值格式
        createStyledTextCell(row2, 4, unitText, centerStyle); // E列居中，单位
        createStyledTextCell(row2, 5, "", centerStyle); // F列居中

        // 合并BC列的两行
        try {
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex + 1, 1, 2));
        } catch (Exception e) {
            log.warn("合并总计区域BC列失败: {}", e.getMessage());
        }

        // 添加表格边框
        addTableBorders(sheet, startRowIndex, currentRowIndex - 1, 1, 5);

        return currentRowIndex;
    }

    /**
     * 填充发货信息（新版本）
     */
    private void fillDeliveryInfoNew(Sheet sheet, YardListDO yardList, int startRowIndex) {
        // 发货日期和签收人在同一行
        Row infoRow = sheet.createRow(startRowIndex);

        // 设置行高为25磅（25 * 20 = 500，因为POI使用1/20磅作为单位）
        infoRow.setHeight((short) (25 * 20));

        // 创建居左对齐样式，使用微软雅黑字体
        Workbook workbook = sheet.getWorkbook();

        // 创建微软雅黑字体
        Font microsoftYaheiFont = workbook.createFont();
        microsoftYaheiFont.setFontName("微软雅黑");
        microsoftYaheiFont.setFontHeightInPoints((short) 11); // 字号11

        CellStyle leftAlignStyle = workbook.createCellStyle();
        leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
        leftAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        leftAlignStyle.setFont(microsoftYaheiFont); // 应用微软雅黑字体

        // BC合并列：发货日期（居左显示）
        String deliveryDate = "";
        if (yardList.getDeliveryDate() != null) {
            deliveryDate = yardList.getDeliveryDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        }
        Cell deliveryCell = createTextCell(infoRow, 1, "发货日期：" + deliveryDate);
        deliveryCell.setCellStyle(leftAlignStyle);

        // 合并BC列
        try {
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex, 1, 2));
        } catch (Exception e) {
            log.warn("合并发货日期BC列失败: {}", e.getMessage());
        }

        // EF合并列：签收人（居左显示）
        String recipient = yardList.getRecipient() != null ? yardList.getRecipient() : "";
        Cell recipientCell = createTextCell(infoRow, 4, "签收人：" + recipient + "    ___________________________");
        recipientCell.setCellStyle(leftAlignStyle);

        // 合并EF列
        try {
            sheet.addMergedRegion(new CellRangeAddress(startRowIndex, startRowIndex, 4, 5));
        } catch (Exception e) {
            log.warn("合并签收人EF列失败: {}", e.getMessage());
        }
    }

    /**
     * 为指定区域添加表格边框（保留原有样式）
     */
    private void addTableBorders(Sheet sheet, int startRow, int endRow, int startCol, int endCol) {
        Workbook workbook = sheet.getWorkbook();

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            for (int colIndex = startCol; colIndex <= endCol; colIndex++) {
                Cell cell = row.getCell(colIndex);
                if (cell == null) {
                    cell = row.createCell(colIndex);
                }

                // 获取原有样式，如果没有则创建新样式
                CellStyle originalStyle = cell.getCellStyle();
                CellStyle newStyle = workbook.createCellStyle();

                // 复制原有样式的所有属性
                if (originalStyle != null) {
                    newStyle.cloneStyleFrom(originalStyle);
                }

                // 添加边框
                newStyle.setBorderTop(BorderStyle.THIN);
                newStyle.setBorderBottom(BorderStyle.THIN);
                newStyle.setBorderLeft(BorderStyle.THIN);
                newStyle.setBorderRight(BorderStyle.THIN);

                // 应用新样式
                cell.setCellStyle(newStyle);
            }
        }
    }

    /**
     * 创建文本单元格
     */
    private Cell createTextCell(Row row, int column, String value) {
        Cell cell = row.createCell(column, CellType.STRING);
        cell.setCellValue(value != null ? value : "");
        return cell;
    }

    /**
     * 创建带样式的文本单元格
     */
    private Cell createStyledTextCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column, CellType.STRING);
        cell.setCellValue(value != null ? value : "");
        if (style != null) {
            cell.setCellStyle(style);
        }
        return cell;
    }

    /**
     * 创建带样式的数值单元格
     */
    private Cell createStyledNumberCell(Row row, int column, Number value, CellStyle style) {
        Cell cell = row.createCell(column, CellType.NUMERIC);
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0);
        }
        if (style != null) {
            cell.setCellStyle(style);
        }
        return cell;
    }

    /**
     * 获取单位文本
     */
    private String getUnitText(Integer unit) {
        if (unit == null) return "单位";

        try {
            // 使用字典工具类获取单位文本
            return cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils.parseDictDataLabel("crm_product_unit", unit);
        } catch (Exception e) {
            log.warn("获取单位字典失败，unit: {}, error: {}", unit, e.getMessage());
            // 如果字典获取失败，使用备用逻辑
            switch (unit) {
                case 1: return "个";
                case 2: return "块";
                case 3: return "只";
                case 4: return "把";
                case 5: return "枚";
                case 6: return "瓶";
                case 7: return "盒";
                case 8: return "台";
                case 9: return "吨";
                case 10: return "千克";
                case 11: return "米";
                case 12: return "箱";
                case 13: return "套";
                default: return "单位";
            }
        }
    }

}