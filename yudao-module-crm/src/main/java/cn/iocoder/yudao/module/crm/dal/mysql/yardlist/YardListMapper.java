package cn.iocoder.yudao.module.crm.dal.mysql.yardlist;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.crm.dal.dataobject.yardlist.YardListDO;
import cn.iocoder.yudao.module.crm.enums.common.CrmBizTypeEnum;
import cn.iocoder.yudao.module.crm.util.CrmPermissionUtils;

import cn.hutool.core.util.StrUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo.*;

/**
 * CRM码单主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YardListMapper extends BaseMapperX<YardListDO> {

    default PageResult<YardListDO> selectPage(YardListPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<YardListDO>()
                .eqIfPresent(YardListDO::getContractId, reqVO.getContractId())
                .eqIfPresent(YardListDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(YardListDO::getOwnerUserId, reqVO.getOwnerUserId())
                .betweenIfPresent(YardListDO::getDeliveryDate, reqVO.getDeliveryDate())
                .likeIfPresent(YardListDO::getProductName, reqVO.getProductName())
                .eqIfPresent(YardListDO::getRecipient, reqVO.getRecipient())
                .eqIfPresent(YardListDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(YardListDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(YardListDO::getId));
    }

    default PageResult<YardListDO> selectPage(YardListPageReqVO reqVO, Long userId, Integer sceneType) {
        MPJLambdaWrapperX<YardListDO> query = new MPJLambdaWrapperX<>();

        // 如果没有指定场景类型，默认显示所有有权限的数据
        if (sceneType == null) {
            // 不是CRM管理员的情况下，需要过滤数据权限
            if (!CrmPermissionUtils.isCrmAdmin()) {
                // 查询用户有权限的码单ID（包括已删除负责人的码单）
                query.apply("(owner_user_id = {0} " + // 自己负责的
                        "OR EXISTS (SELECT 1 FROM crm_permission p WHERE p.biz_type = {1} AND p.biz_id = id AND p.user_id = {2}) " + // 自己参与的
                        "OR owner_user_id IN (" + // 下属负责的（包括已删除的下属）
                            "SELECT u.id FROM system_users u " +
                            "INNER JOIN system_dept d ON u.dept_id = d.id " +
                            "WHERE d.leader_user_id = {3} OR d.parent_id IN (" +
                                "SELECT d2.id FROM system_dept d2 WHERE d2.leader_user_id = {4}" +
                            ")" +
                        "))",
                        userId, CrmBizTypeEnum.CRM_YARD_LIST.getType(), userId, userId, userId);
            }
        } else {
            // 拼接数据权限的查询条件
            CrmPermissionUtils.appendPermissionCondition(query, CrmBizTypeEnum.CRM_YARD_LIST.getType(),
                    YardListDO::getId, userId, sceneType);
        }

        // 拼接自身的查询条件
        query.selectAll(YardListDO.class)
                .eqIfPresent(YardListDO::getContractId, reqVO.getContractId())
                .eqIfPresent(YardListDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(YardListDO::getOwnerUserId, reqVO.getOwnerUserId())
                .betweenIfPresent(YardListDO::getDeliveryDate, reqVO.getDeliveryDate())
                .likeIfPresent(YardListDO::getProductName, reqVO.getProductName())
                .eqIfPresent(YardListDO::getRecipient, reqVO.getRecipient())
                .eqIfPresent(YardListDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(YardListDO::getCreateTime, reqVO.getCreateTime());

        // 如果有合同编号搜索条件，需要关联查询
        if (StrUtil.isNotBlank(reqVO.getContractNo())) {
            query.apply("EXISTS (SELECT 1 FROM crm_contract c WHERE c.id = contract_id AND c.no LIKE {0})",
                    "%" + reqVO.getContractNo() + "%");
        }

        return selectJoinPage(reqVO, YardListDO.class, query.orderByDesc(YardListDO::getId));
    }

}