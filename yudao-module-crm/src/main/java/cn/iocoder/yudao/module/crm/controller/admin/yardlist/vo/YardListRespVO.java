package cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - CRM码单主 Response VO")
@Data
@ExcelIgnoreUnannotated
public class YardListRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27729")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12257")
    @ExcelProperty("合同编号")
    private Long contractId;

    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19496")
    @ExcelProperty("客户编号")
    private Long customerId;

    @Schema(description = "负责人的用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14220")
    @ExcelProperty("负责人的用户编号")
    private Long ownerUserId;

    @Schema(description = "发货日期")
    @ExcelProperty("发货日期")
    private LocalDate deliveryDate;

    @Schema(description = "货品名称", example = "芋艿")
    @ExcelProperty("货品名称")
    private String productName;

    @Schema(description = "总匹数")
    @ExcelProperty("总匹数")
    private Integer totalPieces;

    @Schema(description = "总米数")
    @ExcelProperty("总米数")
    private BigDecimal totalMeters;

    @Schema(description = "签收人")
    @ExcelProperty("签收人")
    private String recipient;

    @Schema(description = "单位", example = "1")
    @ExcelProperty("单位")
    private Integer unit;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}