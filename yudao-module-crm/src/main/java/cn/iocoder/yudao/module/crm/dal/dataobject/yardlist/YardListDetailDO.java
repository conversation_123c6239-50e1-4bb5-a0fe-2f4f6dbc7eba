package cn.iocoder.yudao.module.crm.dal.dataobject.yardlist;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * CRM码单子表（款号包号详情） DO
 *
 * <AUTHOR>
 */
@TableName("crm_yard_list_detail")
@KeySequence("crm_yard_list_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YardListDetailDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 码单主表ID
     */
    private Long yardListId;
    /**
     * 款号
     */
    private String styleNo;
    /**
     * 包号
     */
    private String packageNo;
    /**
     * 数量（米数）
     */
    private BigDecimal quantity;

}