package cn.iocoder.yudao.module.crm.controller.admin.yardlist.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - CRM码单主分页 Request VO")
@Data
public class YardListPageReqVO extends PageParam {

    @Schema(description = "合同编号", example = "12257")
    private Long contractId;

    @Schema(description = "合同编号（搜索用）", example = "*********")
    private String contractNo;

    @Schema(description = "客户编号", example = "19496")
    private Long customerId;

    @Schema(description = "负责人的用户编号", example = "14220")
    private Long ownerUserId;

    @Schema(description = "发货日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] deliveryDate;

    @Schema(description = "货品名称", example = "芋艿")
    private String productName;

    @Schema(description = "签收人")
    private String recipient;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "场景类型", example = "1")
    private Integer sceneType;

}