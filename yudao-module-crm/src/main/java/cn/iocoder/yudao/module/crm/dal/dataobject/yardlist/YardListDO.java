package cn.iocoder.yudao.module.crm.dal.dataobject.yardlist;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * CRM码单主 DO
 *
 * <AUTHOR>
 */
@TableName("crm_yard_list")
@KeySequence("crm_yard_list_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YardListDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 合同编号
     */
    private Long contractId;
    /**
     * 客户编号
     */
    private Long customerId;
    /**
     * 负责人的用户编号
     */
    private Long ownerUserId;
    /**
     * 发货日期
     */
    private LocalDate deliveryDate;
    /**
     * 货品名称
     */
    private String productName;
    /**
     * 总匹数
     */
    private Integer totalPieces;
    /**
     * 总米数
     */
    private BigDecimal totalMeters;
    /**
     * 签收人
     */
    private String recipient;
    /**
     * 单位
     */
    private Integer unit;
    /**
     * 备注
     */
    private String remark;


}