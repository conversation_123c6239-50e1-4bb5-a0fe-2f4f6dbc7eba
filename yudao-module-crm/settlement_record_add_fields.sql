-- 为结算单表添加合同ID、客户ID、负责人ID字段
ALTER TABLE `crm_settlement_record` 
ADD COLUMN `contract_id` bigint DEFAULT NULL COMMENT '合同编号' AFTER `remark`,
ADD COLUMN `customer_id` bigint DEFAULT NULL COMMENT '客户编号' AFTER `contract_id`,
ADD COLUMN `owner_user_id` bigint DEFAULT NULL COMMENT '负责人的用户编号' AFTER `customer_id`;

-- 添加索引以提高查询性能
ALTER TABLE `crm_settlement_record`
ADD KEY `idx_contract_id` (`contract_id`) USING BTREE COMMENT '合同ID索引',
ADD KEY `idx_customer_id` (`customer_id`) USING BTREE COMMENT '客户ID索引',
ADD KEY `idx_owner_user_id` (`owner_user_id`) USING BTREE COMMENT '负责人ID索引';
