import * as echarts from 'echarts/core'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  MapChart,
  <PERSON>ctorialBar<PERSON>hart,
  <PERSON><PERSON><PERSON>,
  RadarChart
} from 'echarts/charts'

import {
  AriaComponent,
  GridComponent,
  LegendComponent,
  ParallelComponent,
  PolarComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent
} from 'echarts/components'

import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  <PERSON>llelComponent,
  VisualMapComponent,
  BarChart,
  LineChart,
  PieChart,
  MapChart,
  CanvasRenderer,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  FunnelChart
])

export default echarts
