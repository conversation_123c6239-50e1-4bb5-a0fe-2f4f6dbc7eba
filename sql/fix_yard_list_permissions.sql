-- 为码单功能添加菜单权限和数据权限

-- 1. 添加码单管理菜单权限
-- 首先查找CRM菜单的ID
SET @crmMenuId = (SELECT id FROM system_menu WHERE name = 'CRM' AND type = 1 LIMIT 1);

-- 检查是否找到CRM菜单
SELECT CASE
    WHEN @crmMenuId IS NULL THEN 'ERROR: CRM菜单未找到，请检查system_menu表中是否存在name=CRM且type=1的记录'
    ELSE CONCAT('找到CRM菜单ID: ', @crmMenuId)
END as status;

-- 只有找到CRM菜单时才添加码单管理菜单
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status, component_name
)
SELECT
    '码单管理', '', 2, 0, @crmMenuId,
    'yard-list', '', 'crm/yardlist/index', 0, 'YardList'
WHERE @crmMenuId IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM system_menu WHERE name = '码单管理' AND component = 'crm/yardlist/index');

-- 获取刚插入的菜单ID
SET @parentId = LAST_INSERT_ID();

-- 添加码单管理的操作按钮权限
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES 
('码单查询', 'crm:yard-list:query', 3, 1, @parentId, '', '', '', 0),
('码单创建', 'crm:yard-list:create', 3, 2, @parentId, '', '', '', 0),
('码单更新', 'crm:yard-list:update', 3, 3, @parentId, '', '', '', 0),
('码单删除', 'crm:yard-list:delete', 3, 4, @parentId, '', '', '', 0),
('码单导出', 'crm:yard-list:export', 3, 5, @parentId, '', '', '', 0);

-- 2. 为现有的码单数据创建权限记录
-- 只为那些还没有权限记录的码单创建权限
INSERT INTO crm_permission (biz_type, biz_id, user_id, level, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    10 as biz_type,  -- CRM_YARD_LIST 的类型值
    yl.id as biz_id,
    yl.owner_user_id as user_id,
    1 as level,  -- OWNER 级别
    yl.creator,
    NOW() as create_time,
    yl.updater,
    NOW() as update_time,
    0 as deleted,
    yl.tenant_id
FROM crm_yard_list yl
WHERE yl.owner_user_id IS NOT NULL
  AND yl.deleted = 0
  AND NOT EXISTS (
    SELECT 1 FROM crm_permission p 
    WHERE p.biz_type = 10 
      AND p.biz_id = yl.id 
      AND p.user_id = yl.owner_user_id
      AND p.deleted = 0
  );

-- 3. 为超级管理员角色添加码单权限
-- 查找超级管理员角色ID（通常是1）
INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    1 as role_id,  -- 超级管理员角色ID
    m.id as menu_id,
    'system' as creator,
    NOW() as create_time,
    'system' as updater,
    NOW() as update_time,
    0 as deleted,
    1 as tenant_id  -- 根据实际租户ID调整
FROM system_menu m 
WHERE m.permission LIKE 'crm:yard-list:%' 
   OR (m.name = '码单管理' AND m.component = 'crm/yardlist/index')
   AND NOT EXISTS (
     SELECT 1 FROM system_role_menu rm 
     WHERE rm.role_id = 1 AND rm.menu_id = m.id AND rm.deleted = 0
   );

-- 4. 为CRM管理员角色添加码单权限（如果存在CRM管理员角色）
-- 这里假设CRM管理员角色ID为某个特定值，需要根据实际情况调整
-- INSERT INTO system_role_menu (role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
-- SELECT 
--     [CRM_ADMIN_ROLE_ID] as role_id,  -- 替换为实际的CRM管理员角色ID
--     m.id as menu_id,
--     'system' as creator,
--     NOW() as create_time,
--     'system' as updater,
--     NOW() as update_time,
--     0 as deleted,
--     1 as tenant_id
-- FROM system_menu m 
-- WHERE m.permission LIKE 'crm:yard-list:%' 
--    OR (m.name = '码单管理' AND m.component = 'crm/yardlist/index')
--    AND NOT EXISTS (
--      SELECT 1 FROM system_role_menu rm 
--      WHERE rm.role_id = [CRM_ADMIN_ROLE_ID] AND rm.menu_id = m.id AND rm.deleted = 0
--    );

-- 执行完成后的验证查询
-- 验证菜单是否创建成功
SELECT id, name, permission, type, parent_id FROM system_menu WHERE name = '码单管理' OR permission LIKE 'crm:yard-list:%';

-- 验证权限记录是否创建成功
SELECT COUNT(*) as yard_list_permission_count FROM crm_permission WHERE biz_type = 10;

-- 验证角色菜单关联是否创建成功
SELECT COUNT(*) as role_menu_count FROM system_role_menu rm 
JOIN system_menu m ON rm.menu_id = m.id 
WHERE m.permission LIKE 'crm:yard-list:%' OR (m.name = '码单管理' AND m.component = 'crm/yardlist/index');
