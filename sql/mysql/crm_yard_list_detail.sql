-- 码单子表（款号包号详情表）
DROP TABLE IF EXISTS `crm_yard_list_detail`;
CREATE TABLE `crm_yard_list_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `yard_list_id` bigint NOT NULL COMMENT '码单主表ID',
  
  -- 码单子表特有字段
  `style_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '款号',
  `package_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '包号',
  `quantity` decimal(10,3) NOT NULL DEFAULT 0.000 COMMENT '数量（米数）',
  
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_yard_list_id`(`yard_list_id` ASC) USING BTREE COMMENT '码单主表ID索引',
  INDEX `idx_style_no`(`style_no` ASC) USING BTREE COMMENT '款号索引',
  INDEX `idx_package_no`(`package_no` ASC) USING BTREE COMMENT '包号索引',
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE COMMENT '租户索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CRM码单子表（款号包号详情）';

SET FOREIGN_KEY_CHECKS = 1;
