-- 码单主表
DROP TABLE IF EXISTS `crm_yard_list`;
CREATE TABLE `crm_yard_list`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` bigint NOT NULL COMMENT '合同编号',
  `customer_id` bigint NOT NULL COMMENT '客户编号',
  `owner_user_id` bigint NOT NULL COMMENT '负责人的用户编号',
  
  -- 新增的码单特有字段
  `delivery_date` date NULL DEFAULT NULL COMMENT '发货日期',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '货品名称',
  `total_pieces` int NULL DEFAULT 0 COMMENT '总匹数',
  `total_meters` decimal(10,3) NULL DEFAULT 0.000 COMMENT '总米数',
  `recipient` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '签收人',
  `unit` int NULL DEFAULT NULL COMMENT '单位',

  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE COMMENT '租户索引',
  INDEX `idx_contract_id`(`contract_id` ASC) USING BTREE COMMENT '合同ID索引',
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE COMMENT '客户ID索引',
  INDEX `idx_owner_user_id`(`owner_user_id` ASC) USING BTREE COMMENT '负责人ID索引',
  INDEX `idx_delivery_date`(`delivery_date` ASC) USING BTREE COMMENT '发货日期索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'CRM码单主表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
