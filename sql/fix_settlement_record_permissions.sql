-- 为现有的结算单数据创建权限记录
-- 只为那些还没有权限记录的结算单创建权限

INSERT INTO crm_permission (biz_type, biz_id, user_id, level, creator, create_time, updater, update_time, deleted, tenant_id)
SELECT 
    9 as biz_type,  -- CRM_SETTLEMENT_RECORD 的类型值
    sr.id as biz_id,
    sr.owner_user_id as user_id,
    1 as level,  -- OWNER 级别
    sr.creator,
    NOW() as create_time,
    sr.updater,
    NOW() as update_time,
    0 as deleted,
    sr.tenant_id
FROM crm_settlement_record sr
WHERE sr.owner_user_id IS NOT NULL
  AND sr.deleted = 0
  AND NOT EXISTS (
    SELECT 1 FROM crm_permission p 
    WHERE p.biz_type = 9 
      AND p.biz_id = sr.id 
      AND p.user_id = sr.owner_user_id
      AND p.deleted = 0
  );
